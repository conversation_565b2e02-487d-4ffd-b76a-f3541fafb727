{"name": "nprogress", "repo": "rstacruz/nprogress", "description": "slim progress bar", "version": "0.2.0", "keywords": ["progress", "bar", "spinner"], "license": "MIT", "main": ["nprogress.js", "nprogress.css"], "scripts": ["nprogress.js"], "styles": ["nprogress.css"], "ignore": ["**/.*", "node_modules", "components", "package.json", "test", "vendor"], "homepage": "https://github.com/rstacruz/nprogress", "_release": "0.2.0", "_resolution": {"type": "version", "tag": "v0.2.0", "commit": "d3699f2104f80b4bbb8e2500f7ab939755b2b66b"}, "_source": "https://github.com/rstacruz/nprogress.git", "_target": "^0.2.0", "_originalSource": "nprogress"}