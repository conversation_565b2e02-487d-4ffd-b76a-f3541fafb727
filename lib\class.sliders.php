<?php

// touring sliders téren és időn át!

class slider {
    
    private $id;
    private $kep;
    private $datum;
    private $focim_1;
    private $focim_2;
    private $focim_3;
    private $focim_4;
    private $alcim_1;
    private $alcim_2;
    private $alcim_3;
    private $alcim_4;
    private $url;
    private $aktiv;
    
    public function __construct($id) {
        $pdo = connect_pdo();
        $sql_selslider = "SELECT * FROM sliders WHERE sliders_id = :id";
        $params_slider = array(':id'=>$id);
        $stmt_slider = $pdo->prepare($sql_selslider);
        bindSqlPArams($stmt_slider, $params_slider);
        $ret_selslider = $stmt_slider->fetchAll(PDO::FETCH_ASSOC);
        foreach ($ret_selslider as $rslider) {
            $this->id = $id;
            $this->kep[0] = $rslider['sliders_kep'];
            $this->datum = $rslider['sliders_datum'];
            $this->focim_1 = $rslider['sliders_focim_1'];
            $this->focim_2 = $rslider['sliders_focim_2'];
            $this->focim_3 = $rslider['sliders_focim_3'];
            $this->focim_4 = $rslider['sliders_focim_4'];
            $this->alcim_1 = $rslider['sliders_alcim_1'];
            $this->alcim_2 = $rslider['sliders_alcim_2'];
            $this->alcim_3 = $rslider['sliders_alcim_3'];
            $this->alcim_4 = $rslider['sliders_alcim_4'];
            $this->url = $rslider['sliders_url'];
            $this->aktiv = $rslider['sliders_aktiv'];
        }
    }
    
    //getterek
    
    function getId() {
        return $this->id;
    }
    
    function getKep() {
        return $this->kep;
    }
    
    function getDatum() {
        return $this->datum;
    }
    
    function getFocim1() {
        return $this->focim_1;
    }
    
    function getFocim2() {
        return $this->focim_2;
    }
    
    function getFocim3() {
        return $this->focim_3;
    }
    
    function getFocim4() {
        return $this->focim_4;
    }
    
    function getAlcim1() {
        return $this->alcim_1;
    }
    
    function getAlcim2() {
        return $this->alcim_2;
    }
    
    function getAlcim3() {
        return $this->alcim_3;
    }
    
    function getAlcim4() {
        return $this->alcim_4;
    }
    
    function getURL() {
        return $this->url;
    }
    
    function getAktiv() {
        return $this->aktiv;
    }
    
    //setterek
    
    function setKep($kep) {
        $this->kep = $kep;
    }
    
    function setDatum($datum) {
        $this->datum = date("Y-m-d",$datum);
    }
    
    function setFocim1($focim) {
        $this->focim_1 = $focim;
    }
    
    function setFocim2($focim) {
        $this->focim_2 = $focim;
    }
    
    function setFocim3($focim) {
        $this->focim_3 = $focim;
    }
    
    function setFocim4($focim) {
        $this->focim_4 = $focim;
    }
    
    function setAlcim1($alcim) {
        $this->alcim_1 = $alcim;
    }
    
    function setAlcim2($alcim) {
        $this->alcim_2 = $alcim;
    }
    
    function setAlcim3($alcim) {
        $this->alcim_3 = $alcim;
    }
    
    function setAlcim4($alcim) {
        $this->alcim_4 = $alcim;
    }
    
    function setURL($url) {
        $this->url = $url;
    }
    
    function setAktiv($aktiv) {
        $this->aktiv = $aktiv;
    }
    
    function update() {
        $pdo = connect_pdo();        
        $kep = $this->kep[0];
        
        $sql_updslider = "UPDATE sliders SET sliders_kep = :kep, sliders_datum = :datum, sliders_focim_1 = :focim_1, sliders_focim_2 = :focim_2, sliders_focim_3 = :focim_3, sliders_focim_4 = :focim_4, sliders_alcim_1 = :alcim_1, sliders_alcim_2 = :alcim_2, sliders_alcim_3 = :alcim_3, sliders_alcim_4 = :alcim_4, sliders_url = :url, sliders_aktiv = :aktiv WHERE sliders_id = :id";
        $params_slider = array(':kep'=>$kep, ':datum'=>$this->datum, ':focim_1'=>$this->focim_1, ':focim_2'=>$this->focim_2, ':focim_3'=>$this->focim_3, ':focim_4'=>$this->focim_4, ':alcim_1'=>$this->alcim_1, ':alcim_2'=>$this->alcim_2, ':alcim_3'=>$this->alcim_3, ':alcim_4'=>$this->alcim_4, ':url'=>$this->url, ':aktiv'=>$this->aktiv, ':id'=>$this->id);
        $stmt_slider = $pdo->prepare($sql_updslider);
        
        if ( bindSqlPArams($stmt_slider, $params_slider) ) {
            return TRUE;
        } else {
            return FALSE;
        }
    }
}