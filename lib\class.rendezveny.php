<?php

//touring rendezvény WAAAAAAAGH!!!

class rendezveny {
    
    private $id;
    private $nev_1;
    private $nev_2;
    private $nev_3;
    private $nev_4;
    private $seo_key;
    private $seo_desc;
    private $kep;
    private $desc_1;
    private $desc_2;
    private $desc_3;
    private $desc_4;
    private $galeria;
    private $datetime;
    private $aktiv;

    public function __construct($id) {
        $pdo = connect_pdo();
        $sql_selrend = "SELECT * FROM rendezveny WHERE rendezveny_id = :id";
        $params_rnd = array(':id'=>$id);
        $stmt_rnd = $pdo->prepare($sql_selrend);
        bindSqlPArams($stmt_rnd, $params_rnd);
        $ret_selrend = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($ret_selrend as $rrend) {
            $this->id = $id;
            $this->nev_1 = $rrend['rendezveny_nev_1'];
            $this->nev_2 = $rrend['rendezveny_nev_2'];
            $this->nev_3 = $rrend['rendezveny_nev_3'];
            $this->nev_4 = $rrend['rendezveny_nev_4'];
            $this->seo_key = $rrend['rendezveny_seo_key'];
            $this->seo_desc = $rrend['rendezveny_seo_desc'];
            $this->kep[0] = $rrend['rendezveny_kep'];
            $this->desc_1 = $rrend['rendezveny_desc_1'];
            $this->desc_2 = $rrend['rendezveny_desc_2'];
            $this->desc_3 = $rrend['rendezveny_desc_3'];
            $this->desc_4 = $rrend['rendezveny_desc_4'];
            $this->galeria = $rrend['rendezveny_galeria'];
            $this->datetime = $rrend['rendezveny_datetime'];
            $this->aktiv = $rrend['rendezveny_aktiv'];
        }
    }
    
    //getterek
    
    function getId() {
        return $this->id;
    }
    
    function getNev1() {
        return $this->nev_1;
    }
    
    function getNev2() {
        return $this->nev_2;
    }
    
    function getNev3() {
        return $this->nev_3;
    }
    
    function getNev4() {
        return $this->nev_4;
    }
    
    function getSeoKey() {
        return $this->seo_key;
    }
    
    function getSeoDesc() {
        return $this->seo_desc;
    }
    
    function getKep() {
        return $this->kep[0];
    }
    
    function getDesc1() {
        return $this->desc_1;
    }
    
    function getDesc2() {
        return $this->desc_2;
    }
    
    function getDesc3() {
        return $this->desc_3;
    }
    
    function getDesc4() {
        return $this->desc_4;
    }
    
    function getGaleria() {
        return $this->galeria;
    }
    
    function getDateTime() {
        return $this->datetime;
    }
    
    function getAktiv() {
        return $this->aktiv;
    }


    //setterek
    
    function setNev1($nev) {
        $this->nev_1 = $nev;
    }
    
    function setNev2($nev) {
        $this->nev_2 = $nev;
    }
    
    function setNev3($nev) {
        $this->nev_3 = $nev;
    }
    
    function setNev4($nev) {
        $this->nev_4 = $nev;
    }
    
    function setSeoKey($seo_key) {
        $this->seo_key = $seo_key;
    }
    
    function setSeoDesc($seo_desc) {
        $this->seo_desc = $seo_desc;
    }
    
    function setKep($kep) {
        $this->kep = $kep;
    }
    
    function setDesc1($desc) {
        $this->desc_1 = $desc;
    }
    
    function setDesc2($desc) {
        $this->desc_2 = $desc;
    }
    
    function setDesc3($desc) {
        $this->desc_3 = $desc;
    }
    
    function setDesc4($desc) {
        $this->desc_4 = $desc;
    }
    
    function setGaleria($galeria) {
        $this->galeria = $galeria;
    }
    
    function setDateTime($datetime) {
        $this->datetime = date("Y-m-d H:i:s",$datetime);
    }
    
    function setAktiv($aktiv) {
        $this->aktiv = $aktiv;
    }
            
    function update() {
        $pdo = connect_pdo();
        $kep = $this->kep[0];
        
        $sql_updrend = "UPDATE rendezveny SET rendezveny_nev_1 = :nev_1, rendezveny_nev_2 = :nev_2, rendezveny_nev_3 = :nev_3', rendezveny_nev_4 = :nev_4, rendezveny_seo_key = :seo_key, rendezveny_seo_desc = :seo_desc, rendezveny_kep = :kep, rendezveny_desc_1 = :desc_1, rendezveny_desc_2 = :desc_2, rendezveny_desc_3 = :desc_3, rendezveny_desc_4 = :desc_4, rendezveny_galeria = :galeria, rendezveny_datetime = :datetime, rendezveny_aktiv = :aktiv WHERE rendezveny_id = :id";
        $params_rnd = array(':nev_1'=>$this->nev_1, ':nev_2'=>$this->nev_2, ':nev_3'=>$this->nev_3, ':nev_4'=>$this->nev_4, ':seo_key'=>$this->seo_key, ':seo_desc'=>$this->seo_desc, ':kep'=>$kep, ':desc_1'=>$this->desc_1, ':desc_2'=>$this->desc_2, ':desc_3'=>$this->desc_3, ':desc_4'=>$this->desc_4, ':galeria'=>$this->galeria, ':datetime'=>$this->datetime, ':aktiv'=>$this->aktiv, ':id'=>$this->id);
        $stmt_rnd = $pdo->prepare($sql_updrend);
        
        if ( bindSqlPArams($stmt_rnd, $params_rnd) ) {
            return TRUE;
        } else {
            return FALSE;
        }
    }
}