<?php

//touring ajánlatkérések és spamküldések

class ajanlat {
    
    private $id;
    private $nev;
    private $mail;
    private $tel;
    private $datum;
    private $datum_tol;
    private $datum_ig;
    private $felnott;
    private $gyerek;
    private $szoveg;
    private $valasz;
    private $datum_valasz;
    
    public function __construct($id) {
        $pdo = connect_pdo();
        $sql_selajan = "SELECT * FROM ajanlat WHERE ajanlat_id = :id";
        $params_aj = array(':id'=>$id);
        $stmt_aj = $pdo->prepare($sql_selajan);
        bindSqlPArams($stmt_aj, $params_aj);
        $ret_selajan = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($ret_selajn as $rajan) {
            $this->id = $id;
            $this->nev = $rajan['ajanlat_nev'];
            $this->mail = $rajan['ajanlat_mail'];
            $this->tel = $rajan['ajanlat_tel'];
            $this->datum = $rajan['ajanlat_datum'];
            $this->datum_tol = $rajan['ajanlat_datum_tol'];
            $this->datum_ig = $rajan['ajanlat_datum_ig'];
            $this->felnott = $rajan['ajanlat_felnott'];
            $this->gyerek = $rajan['ajanlat_gyerek'];
            $this->szoveg = $rajan['ajanlat_szoveg'];
            $this->valasz = $rajan['ajanlat_valasz'];
            $this->datum_valasz = $rajan['ajanlat_datum_valasz'];
        }
    }
    
    //getterek
    
    function getId() {
        return $this->id;
    }
    
    function getNev() {
        return $this->nev;
    }
    
    function getMail() {
        return $this->mail;
    }
    
    function getTel() {
        return $this->tel;
    }
    
    function getDatum() {
        return $this->datum;
    }
    
    function getDatumTol() {
        return $this->datum_tol;
    }
    
    function getDatumIg() {
        return $this->datum_ig;
    }
    
    function getFelnott() {
        return $this->felnott;
    }
    
    function getGyerek() {
        return $this->gyerek;
    }
    
    function getSzoveg() {
        return $this->szoveg;
    }
    
    function getValasz() {
        return $this->valasz;
    }
    
    function getDatumValasz() {
        return $this->datum_valasz;
    }
    
    //setterek
    
    function setNev($nev) {
        $this->nev = $nev;
    }
    
    function setMail($mail) {
        $this->mail = $mail;
    }
    
    function setTel($tel) {
        $this->tel = $tel;
    }
    
    function setDatum($datum) {
        $this->datum = $datum;
    }
    
    function setDatumTol($datum) {
        $this->datum_tol = $datum;
    }
    
    function setDatumIg($datum) {
        $this->datum_ig = $datum;
    }
    
    function setFelnott($felnott) {
        $this->felnott = $felnott;
    }
    
    function setGyerek($gyerek) {
        $this->gyerek = $gyerek;
    }
    
    function setSzoveg($szoveg) {
        $this->szoveg = $szoveg;
    }
    
    function setValasz($valasz) {
        $this->valasz = $valasz;
    }
    
    function setDatumValasz($datum) {
        $this->datum_valasz = $datum;
    }
    
    function update() {
        $pdo = connect_pdo();
        $sql_updajan = "UPDATE ajanlat SET ajanlat_nev = :nev, ajanlat_mail = :mail, ajanlat_tel = :tel, ajanlat_datum = :datum, ajanlat_datum_tol = :datum_tol, ajanlat_datum_ig = :datum_ig, ajanlat_felnott = :felnott, ajanlat_gyerek = :gyerek, ajanlat_szoveg = :szoveg, ajanlat_valasz = :valasz, ajanlat_datum_valasz = :datum_valasz WHERE ajanlat_id = :id";
        $params_aj = array(':nev'=>$this->nev, ':mail'=>$this->mail, ':tel'=>$this->tel, ':datum'=>$this->datum, ':datum_tol'=>$this->datum_tol, ':datum_ig'=>$this->datum_ig, ':felnott'=>$this->felnott, ':gyerek'=>$this->gyerek, ':szoveg'=>$this->szoveg, ':valasz'=>$this->valasz, ':datum_valasz'=>$this->datum_valasz, ':id'=>$this->id);
        $stmt_aj = $pdo->prepare($sql_updajan);
        //bindSqlPArams($stmt_aj, $params_aj);
        
        if ( bindSqlPArams($stmt_aj, $params_aj) ) {
            return TRUE;
        } else {
            return FALSE;
        }
    }
}