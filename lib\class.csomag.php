<?php

class csomag {
    
    private $id;
    private $nev_1;
    private $nev_2;
    private $nev_3;
    private $nev_4;
    private $kep;
    private $ar;
    private $felnott;
    private $gyerek;
    private $nap;
    private $desc_1;
    private $desc_2;
    private $desc_3;
    private $desc_4;
    private $etkezes_1;
    private $etkezes_2;
    private $etkezes_3;
    private $etkezes_4;
    private $egyeb_1;
    private $egyeb_2;
    private $egyeb_3;
    private $egyeb_4;
    private $aktiv;
    private $del;

    public function __construct($id) {
        $pdo = connect_pdo();
        $sql_selcsomag = "SELECT * FROM csomag WHERE csomag_id = :id";
        $params_csm = array(':id'=>$id);
        $stmt_csm = $pdo->prepare($sql_selcsomag);
        bindSqlPArams($stmt_csm, $params_csm);
        $ret_selcsomag = $stmt_csm->fetchAll(PDO::FETCH_ASSOC);
        foreach ($ret_selcsomag as $rcsomag) {
            $this->id = $id;
            $this->nev_1 = $rcsomag['csomag_nev_1'];
            $this->nev_2 = $rcsomag['csomag_nev_2'];
            $this->nev_3 = $rcsomag['csomag_nev_3'];
            $this->nev_4 = $rcsomag['csomag_nev_4'];
            $this->kep[] = $rcsomag['csomag_kep'];
            $this->ar = $rcsomag['csomag_ar'];
            $this->felnott = $rcsomag['csomag_felnott'];
            $this->gyerek = $rcsomag['csomag_gyerek'];
            $this->nap = $rcsomag['csomag_nap'];
            $this->desc_1 = $rcsomag['csomag_desc_1'];
            $this->desc_2 = $rcsomag['csomag_desc_2'];
            $this->desc_3 = $rcsomag['csomag_desc_3'];
            $this->desc_4 = $rcsomag['csomag_desc_4'];
            $this->etkezes_1 = $rcsomag['csomag_etkezes_1'];
            $this->etkezes_2 = $rcsomag['csomag_etkezes_2'];
            $this->etkezes_3 = $rcsomag['csomag_etkezes_3'];
            $this->etkezes_4 = $rcsomag['csomag_etkezes_4'];
            $this->egyeb_1 = $rcsomag['csomag_egyeb_1'];
            $this->egyeb_2 = $rcsomag['csomag_egyeb_2'];
            $this->egyeb_3 = $rcsomag['csomag_egyeb_3'];
            $this->egyeb_4 = $rcsomag['csomag_egyeb_4'];
            $this->aktiv = $rcsomag['csomag_aktiv'];
            $this->detl = $rcsomag['csomag_del'];
        }
    }
    
    //getterek
    
    function getId() {
        return $this->id;
    }
    
    function getNev1() {
        return $this->nev_1;
    }
    
    function getNev2() {
        return $this->nev_2;
    }
    
    function getNev3() {
        return $this->nev_3;
    }
    
    function getNev4() {
        return $this->nev_4;
    }
    
    function getKep() {
        return $this->kep;
    }
            
    function getAr() {
        return $this->ar;
    }
    
    function getFelnott() {
        return $this->felnott;
    }
    
    function getGyerek() {
        return $this->gyerek;
    }
    
    function getNap() {
        return $this->nap;
    }
    
    function getDesc1() {
        return $this->desc_1;
    }
    
    function getDesc2() {
        return $this->desc_2;
    }
    
    function getDesc3() {
        return $this->desc_3;
    }
    
    function getDesc4() {
        return $this->desc_4;
    }
    
    function getEtkezes1() {
        return $this->etkezes_1;
    }
    
    function getEtkezes2() {
        return $this->etkezes_2;
    }
    
    function getEtkezes3() {
        return $this->etkezes_3;
    }
    
    function getEtkezes4() {
        return $this->etkezes_4;
    }
    
    function getEgyeb1() {
        return $this->egyeb_1;
    }
    
    function getEgyeb2() {
        return $this->egyeb_2;
    }
    
    function getEgyeb3() {
        return $this->egyeb_3;
    }
    
    function getEgyeb4() {
        return $this->egyeb_4;
    }
    
    function getAktiv() {
        return $this->aktiv;
    }
    
    function getDel() {
        return $this->del;
    }
    
    //setterek
    
    function setNev1($nev) {
        $this->nev_1 = $nev;
    }
    
    function setNev2($nev) {
        $this->nev_2 = $nev;
    }
    
    function setNev3($nev) {
        $this->nev_3 = $nev;
    }
    
    function setNev4($nev) {
        $this->nev_4 = $nev;
    }
    
    function setKep($kep) {
        $this->kep = $kep;
    }
    
    function setAr($ar) {
        $this->ar = $ar;
    }
    
    function setFelnott($felnott) {
        $this->felnott = $felnott;
    }
    
    function setGyerek($gyerek) {
        $this->gyerek = $gyerek;
    }
    
    function setNap($nap) {
        $this->nap = $nap;
    }
    
    function setDesc1($desc) {
        $this->desc_1 = $desc;
    }
    
    function setDesc2($desc) {
        $this->desc_2 = $desc;
    }
    
    function setDesc3($desc) {
        $this->desc_3 = $desc;
    }
    
    function setDesc4($desc) {
        $this->desc_4 = $desc;
    }
    
    function setEtkezes1($etkezes) {
        $this->etkezes_1 = $etkezes;
    }
    
    function setEtkezes2($etkezes) {
        $this->etkezes_2 = $etkezes;
    }
    
    function setEtkezes3($etkezes) {
        $this->etkezes_3 = $etkezes;
    }
    
    function setEtkezes4($etkezes) {
        $this->etkezes_4 = $etkezes;
    }
    
    function setEgyeb1($egyeb) {
        $this->egyeb_1 = $egyeb;
    }
    
    function setEgyeb2($egyeb) {
        $this->egyeb_2 = $egyeb;
    }
    
    function setEgyeb3($egyeb) {
        $this->egyeb_3 = $egyeb;
    }
    
    function setEgyeb4($egyeb) {
        $this->egyeb_4 = $egyeb;
    }
    
    function setAktiv($aktiv) {
        $this->aktiv = $aktiv;
    }
    
    function setDel($del) {
        $this->del = $del;
    }
    
    function update() {
        $pdo = connect_pdo();
        $kep = $this->kep[0];
        
        $sql_updcsomag = "UPDATE csomag SET csomag_nev_1 = :nev_1, csomag_nev_2 = :nev_2, csomag_nev_3 = :nev_3, csomag_nev_4 = :nev_4, csomag_kep = :kep, csomag_ar = :ar, csomag_felnott = :felnott, csomag_gyerek = :gyerek, csomag_nap = :nap, csomag_desc_1 = :desc_1, csomag_desc_2 = :desc_2, csomag_desc_3 = :desc_3, csomag_desc_4 = :desc_4, csomag_etkezes_1 = :etkezes_1, csomag_etkezes_2 = :etkezes_2, csomag_etkezes_3 = :etkezes_3, csomag_etkezes_4 = :etkezes_4, csomag_egyeb_1 = :egyeb_1, csomag_egyeb_2 = :egyeb_2, csomag_egyeb_3 = :egyeb_3, csomag_egyeb_4 = :egyeb_4, csomag_aktiv = :aktiv, csomag_del = :del WHERE csomag_id = :id";
        $params_csm = array(':nev_1'=>$this->nev_1, ':nev_2'=>$this->nev_2, ':nev_3'=>$this->nev_3, ':nev_4'=>$this->nev_4, ':kep'=>$kep, ':ar'=>$this->ar, ':felnott'=>$this->felnott, ':gyerek'=>$this->gyerek, ':nap'=>$this->nap, ':desc_1'=>$this->desc_1, ':desc_2'=>$this->desc_2, ':desc_3'=>$this->desc_3, ':desc_4'=>$this->desc_4, ':etkezes_1'=>$this->etkezes_1, ':etkezes_2'=>$this->etkezes_2, ':etkezes_3'=>$this->etkezes_3, ':etkezes_4'=>$this->etkezes_4, ':egyeb_1'=>$this->egyeb_1, ':egyeb_2'=>$this->egyeb_2, ':egyeb_3'=>$this->egyeb_3, ':egyeb_4'=>$this->egyeb_4, ':aktiv'=>$this->aktiv, ':del'=>$this->del, ':id'=>$this->id);
        $stmt_csm = $pdo->prepare($sql_updcsomag);
        
        if ( bindSqlPArams($stmt_csm, $params_csm) ) {
            return TRUE;
        } else {
            return FALSE;
        }
    }
    
    function printAjanlatContainer($parossag) {
        
        $letszam = $this->getFelnott() + $this->getGyerek();
        print '<div class="tcol-xs-12 tcol-sm-12 tcol-md-6 tcol-lg-6 tcol-xl-3 '.$parossag.'">
                  <div class="ajanlatkep">';
                    if ( @GetImageSize($_SERVER["DOCUMENT_ROOT"]."/csomagkep/".$this->getKep()[0]) && $this->getKep()[0] != "" ) {
                        print '<img src="/csomagkep/'.$this->getKep()[0].'" alt="kep1" />';
                    } else {
                        print '<img src="/uploads/kep_hamarosan.jpg" alt="kep1" />';
                    }
            print '</div>
                  <div class="ajanlatszoveg">
                      <div class="row">
                          <div class="col-xs-6 col-sm-6 col-md-6 col-lg-6 col-xs-offset-1 col-sm-offset-1 col-md-offset-1 col-lg-offset-1 ajanlatfejlec">
                              
                              <span class="ajanlatnev">'.$this->getNev1().'</span><br />
                              <span class="ajanlatar">'.number_format($this->getAr(), 0, ",", ".").'</span> Ft

                          </div>
                          
                          <div class="col-xs-4 col-sm-4 col-md-4 col-lg-4">
                              <a href="csomag.php?id='.$this->getId().'" class="ajanlatgomb-szabad">'.$GLOBALS['_lefoglalom_gomb'].'</a>
                          </div>
                      </div>
                               
                  </div>
             </div>';
    }
    
    function printAjanlatLista() {
        $letszam = $this->getFelnott() + $this->getGyerek();
        print '<div class="col-xs-12 col-sm-12 col-md-12 col-lg-12" style="padding-bottom:15px;">
                  <div class="row">
                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6">
                      <div class="csomagkep">';
                      
                      if ( @GetImageSize($_SERVER["DOCUMENT_ROOT"]."/csomagkep/".$this->getKep()[0]) && $this->getKep()[0] != "" ) {
                         print '<img src="/csomagkep/'.$this->getKep()[0].'" alt="kep1" />';
                      } else {
                         print '<img src="/uploads/kep_hamarosan.jpg" alt="kep1" />';
                      }
                      
                          
               print '</div>
                    </div>
                    
                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-6" style="height:100%;">
                      <div class="csomagszoveg">
                          <div class="row">
                              <div class="col-xs-6 col-sm-6 col-md-6 col-lg-6 col-xs-offset-1 col-sm-offset-1 col-md-offset-1 col-lg-offset-1 csomagfejlec">
                                  
                                  <span class="ajanlatnev">';
            
                                    switch ($GLOBALS['lang']) {
                                        case '1':
                                            print $this->getNev1();
                                            break;
                                        case '2':
                                            print $this->getNev2();
                                            break;
                                        case '3':
                                            print $this->getNev3();
                                            break;
                                        case '4':
                                            print $this->getNev4();
                                            break;
                                        default:
                                            print $this->getNev1();
                                            break;
                                    }
                                    
                           print '</span><br />
                                  <span class="ajanlatar">'.number_format($this->getAr(), 0, ",", ".").'</span> Ft<br />
                               </div>
                               <div class="col-xs-5 col-sm-5 col-md-5 col-lg-5">
                                    <a href="csomag.php?id='.$this->getId().'" class="ajanlatgomb-szabad">'.$GLOBALS['_lefoglalom_gomb'].'</a>
                              </div>
                    
                          </div>
                      </div>
                    </div>
                  </div>
               </div>';
    }
    
    function printCsomagReszlet() {
                       print '<div class="row">'
                                . '<div class="col-xs-12 col-sm-12 col-md-12 col-lg-6" style="overflow:hidden;">';
                                  if ( @GetImageSize($_SERVER["DOCUMENT_ROOT"]."/csomagkep/".$this->getKep()[0]) && $this->getKep()[0] != "" ) {
                                     print '<a href="csomagkep/'.$this->getKep()[0].'" data-lightbox="'.$this->getKep()[0].'"><img src="csomagkep/'.$this->getKep()[0].'" /></a>';
                                  } else {
                                     print '<img src="/uploads/kep_hamarosan.jpg" alt="kep1" />';
                                  }
                                print '</div>'
                                . '<div class="col-xs-12 col-sm-12 col-md-12 col-lg-6">
                                    <div class="etel-nev">';
                                    switch ($GLOBALS['lang']) {
                                        case '1':
                                            print $this->getNev1();
                                            break;
                                        case '2':
                                            print $this->getNev2();
                                            break;
                                        case '3':
                                            print $this->getNev3();
                                            break;
                                        case '4':
                                            print $this->getNev4();
                                            break;
                                        default:
                                            print $this->getNev1();
                                            break;
                                    }
                                  print '</div>
                                         <br />
                                         <div class="ajanlatreszlet">';
                                    switch ($GLOBALS['lang']) {
                                        case '1':
                                            print $this->getDesc1();
                                            break;
                                        case '2':
                                            print $this->getDesc2();
                                            break;
                                        case '3':
                                            print $this->getDesc3();
                                            break;
                                        case '4':
                                            print $this->getDesc4();
                                            break;
                                        default:
                                            print $this->getDesc4();
                                            break;
                                    }
                                 print '</div>
                                        <br />';
                                          $this->getAr();
                                print '</div>'
                           . '</div>';
    }
    
    function printAjanlatOsszesito() {
        $letszam = $this->getFelnott() + $this->getGyerek();
        print '<div class="tcol-xs-12 tcol-sm-12 tcol-md-6 tcol-lg-6 tcol-xl-6" style="min-height:670px;">
                  <div class="ajanlatkep">';
                    if ( @GetImageSize($_SERVER["DOCUMENT_ROOT"]."/csomagkep/".$this->getKep()[0]) && $this->getKep()[0] != "" ) {
                      print '<img src="/csomagkep/'.$this->getKep()[0].'" alt="kep1" />';
                    } else {
                      print '<img src="/uploads/kep_hamarosan.jpg" alt="kep1" />';
                    }
          print '</div>
                  <div class="ajanlatszoveg" style="height:100%;">
                      <div class="row">
                          <div class="col-xs-6 col-sm-6 col-md-6 col-lg-6 col-xs-offset-1 col-sm-offset-1 col-md-offset-1 col-lg-offset-1 ajanlatfejlec">
                              
                              <span class="ajanlatnev">'.$this->getNev1().'</span><br />
                              <span class="ajanlatar">'.number_format($this->getAr(), 0, ",", ".").'</span> Ft

                          </div>
                          
                          <div class="col-xs-4 col-sm-4 col-md-4 col-lg-4">
                              <a href="csomag.php?id='.$this->getId().'" class="ajanlatgomb-szabad">'.$GLOBALS['_lefoglalom_gomb'].'</a>
                          </div>
                      </div>
                      
                      
                      <div class="row">
                          <div class="col-xs-10 col-sm-10 col-md-10 col-lg-10 col-xs-offset-1 col-sm-offset-1 col-md-offset-1 col-lg-offset-1 ajanlatreszlet">
                                Vendégek száma:'.$letszam.'<br />
                                Időtartam: '.$this->getNap().' nap <br />';
                                    switch ($GLOBALS['lang']) {
                                        case '1':
                                            print $this->getDesc1();
                                            break;
                                        case '2':
                                            print $this->getDesc2();
                                            break;
                                        case '3':
                                            print $this->getDesc3();
                                            break;
                                        case '4':
                                            print $this->getDesc4();
                                            break;
                                        default:
                                            print $this->getDesc1();
                                            break;
                                    }
                          print '<br />
                                Étkezés: ';
                                    switch ($GLOBALS['lang']) {
                                        case '1':
                                            print $this->getEtkezes1();
                                            break;
                                        case '2':
                                            print $this->getEtkezes2();
                                            break;
                                        case '3':
                                            print $this->getEtkezes3();
                                            break;
                                        case '4':
                                            print $this->getEtkezes4();
                                            break;
                                        default:
                                            print $this->getEtkezes1();
                                            break;
                                    }
                         print' <br />';
                            switch ($GLOBALS['lang']) {
                                case '1':
                                    print $this->getEgyeb1();
                                    break;
                                case '2':
                                    print $this->getEgyeb2();
                                    break;
                                case '3':
                                    print $this->getEgyeb3();
                                    break;
                                case '4':
                                    print $this->getEgyeb4();
                                    break;
                                default:
                                    print $this->getEgyeb1();
                                    break;
                            }
                         print ' <br />
                         </div>
                      </div>
                      
                  </div>
             </div>';
    }
    
}