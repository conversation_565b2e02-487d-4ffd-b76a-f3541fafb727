<?php

//touring etel nyamnyamnyam

class etel {
    
    private $id;
    private $nev_1;
    private $nev_2;
    private $nev_3;
    private $nev_4;
    private $seo_key;
    private $seo_desc;
    private $ar;
    private $desc_1;
    private $desc_2;
    private $desc_3;
    private $desc_4;
    private $kep;
    private $aktiv;

    public function __construct($id) {
        $pdo = connect_pdo();
        $sql_seletel = "SELECT * FROM etel WHERE etel_id = :id";
        $params_et = array(':id'=>$id);
        $stmt_et = $pdo->prepare($sql_seletel);
        bindSqlPArams($stmt_et, $params_et);
        $ret_seletel = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($ret_seletel as $retel) {
            $this->id = $id;
            $this->nev_1 = $retel['etel_nev_1'];
            $this->nev_2 = $retel['etel_nev_2'];
            $this->nev_3 = $retel['etel_nev_3'];
            $this->nev_4 = $retel['etel_nev_4'];
            $this->seo_key = $retel['etel_seo_key'];
            $this->seo_desc = $retel['etel_seo_desc'];
            $this->ar = $retel['etel_ar'];
            $this->desc_1 = $retel['etel_desc_1'];
            $this->desc_2 = $retel['etel_desc_2'];
            $this->desc_3 = $retel['etel_desc_3'];
            $this->desc_4 = $retel['etel_desc_4'];
            $this->kep[0] = $retel['etel_kep'];
            $this->aktiv = $retel['etel_aktiv'];
        }
    }
    
    //getterek
    
    function getId() {
        return $this->id;
    }
    
    function getNev1() {
        return $this->nev_1;
    }
    
    function getNev2() {
        return $this->nev_2;
    }
    
    function getNev3() {
        return $this->nev_3;
    }
    
    function getNev4() {
        return $this->nev_4;
    }
    
    function getSeoKey() {
        return $this->seo_key;
    }
    
    function getSeoDesc() {
        return $this->seo_desc;
    }
    
    function getAr() {
        return $this->ar;
    }
    
    function getDesc1() {
        return $this->desc_1;
    }
    
    function getDesc2() {
        return $this->desc_2;
    }
    
    function getDesc3() {
        return $this->desc_3;
    }
    
    function getDesc4() {
        return $this->desc_4;
    }
    
    function getKep() {
        return $this->kep;
    }
    
    function getAktiv() {
        return $this->aktiv;
    }


    //setterek
    
    function setNev1($nev) {
        $this->nev_1 = $nev;
    }
    
    function setNev2($nev) {
        $this->nev_2 = $nev;
    }
    
    function setNev3($nev) {
        $this->nev_3 = $nev;
    }
    
    function setNev4($nev) {
        $this->nev_4 = $nev;
    }
    
    function setSeoKey($seo_key) {
        $this->seo_key = $seo_key;
    }
    
    function setSeoDesc($seo_desc) {
        $this->seo_desc = $seo_desc;
    }
    
    function setAr($ar) {
        $this->ar = $ar;
    }
    
    function setDesc1($desc) {
        $this->desc_1 = $desc;
    }
    
    function setDesc2($desc) {
        $this->desc_2 = $desc;
    }
    
    function setDesc3($desc) {
        $this->desc_3 = $desc;
    }
    
    function setDesc4($desc) {
        $this->desc_4 = $desc;
    }
    
    function setKep($kep) {
        $this->kep = $kep;
    }
    
    function setAktiv($aktiv) {
        $this->aktiv = $aktiv;
    }
            
    function update() {
        $kep = $this->kep[0];
        
        $sql_updetel = "UPDATE etel SET etel_nev_1 = :nev_1, etel_nev_2 = :nev_2, etel_nev_3 = :nev_3', etel_nev_4 = :nev_4, etel_seo_key = :seo_key, etel_seo_desc = :seo_desc, etel_ar = :ar, etel_desc_1 = :desc_1, etel_desc_2 = :desc_2, etel_desc_3 = :desc_3, etel_desc_4 = :desc_4, etel_kep = :kep, etel_aktiv = :aktiv WHERE etel_id = :id";
        $params_et = array(':nev_1'=>$this->nev_1, ':nev_2'=>$this->nev_2, ':nev_3'=>$this->nev_3, ':nev_4'=>$this->nev_4, ':seo_key'=>$this->seo_key, ':seo_desc'=>$this->seo_desc, ':ar'=>$this->ar, ':desc_1'=>$this->desc_1, ':desc_2'=>$this->desc_2, ':desc_3'=>$this->desc_3, ':desc_4'=>$this->desc_4, ':kep'=>$kep, ':aktiv'=>$this->aktiv, ':id'=>$this->id);
        $stmt_et = $pdo->prepare($sql_updetel);
        
        if ( bindSqlPArams($stmt_et, $params_et) ) {
            return TRUE;
        } else {
            return FALSE;
        }
    }
    
    function printEtteremEtel() {
        print '<div class="col-xs-12 col-sm-12 col-md-6 col-lg-2 etterem-etel">
                        <div class="etterem-etelkep">
                            <img src="etelkep/tn_'.$this->getKep()[0].'" alt="etelkep" />
                        </div>
                        <div class="etterem-etelnev">';
                            
                            switch ($GLOBALS['lang']) {
                                case "1":
                                    print $this->getNev1();
                                    break;
                                case "2":
                                    print $this->getNev2();
                                    break;
                                case "3":
                                    print $this->getNev3();
                                    break;
                                case "4":
                                    print $this->getNev4();
                                    break;
                                default :
                                    print $this->getNev1();
                                    break;
                            }
        
                 print '</div>
                        <a href="etel.php?id='.$this->getId().'" class="feher-narancsborder-gomb">'.$GLOBALS['_lefoglalom_gomb'].'</a>
                    </div>';
    }
}