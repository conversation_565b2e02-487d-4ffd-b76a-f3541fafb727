<?php
 require_once 'lib/fuggv_inc.php';
 connect_pdo();
 ob_start();
?>
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <!-- Meta, title, CSS, favicons, etc. -->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta http-equiv="content-type" content="text/html; charset=utf-8" />
    <meta http-equiv="content-language" content="hu" />
    
    <?php
      $page = new page("Hotel");
    ?>
    
    <meta name="author" content="Touring Hotel**" />
    <meta name="keywords" content="<? print $page->getSeoKey(); ?>" />
    <meta name="description" content="<? print $page->getSeoDesc(); ?>" />
    <meta name="robots" content="index" />
    <meta name="publisher" content="Touring Hotel**" />
    <meta name="copyright" content="2018" />
    <link rel="canonical" href="www.touringhotel.co.hu" />  
    
    <link rel="stylesheet" type="text/css" href="jquery.zweatherfeed.css" />

    <script type="text/javascript" src="js/jquery.js"></script>
    <script type="text/javascript" src="js/jquery.zweatherfeed.js"></script>

    <title><?php print $_title; ?></title>
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0;" />
    
  <?php print $css_section; ?>
  
  <script type="application/ld+json">
{
  "@context" : "http://schema.org/Place",
  "@type" : "Place",
  "author": "Touring Hotel**",
  "name" : "Touring Hotel**",
  "image" : "touringhotel.co.hu/uploads/footlogo.jpg",
  "description" : "<? print $page->getDesc1(); ?>",
  "description" : "<? print $page->getDesc2(); ?>",
  "description" : "<? print $page->getDesc3(); ?>",
  "description" : "<? print $page->getDesc4(); ?>",
  "url" : "www.touringhotel.co.hu" 
}
</script>

    
  </head>
  <body class="body">
      
      <?php        
        touring_header();
      ?>
      
      <?php
        touring_slider();
      ?>
    
      <div class="row content">
          <div class="col-xs-12 col-sm-8 col-md-8 col-lg-8 col-xs-offset-0 col-sm-offset-2 col-md-offset-2 col-lg-offset-2">
              
              <span class="content-felsocim"><?php print $GLOBALS['_index_felsocim']; ?></span><br />
              <span class="content-focim"><?php print $GLOBALS['_index_focim']; ?></span><br />
              <span class="content-szoveg"><?php print $GLOBALS['_index_szoveg']; ?></span><br />
              <span><?php print '<a href="#specszolg" class="feher-narancsborder-gomb">'.$GLOBALS['_index_gomb'].'</a>'; ?></span>
              
          </div>   
      </div>
      
      <?php
        touring_ajanlatok();
      ?>
      
      <?php
        touring_specszolg();
      ?>
      
      <?php
        touring_velemeny();
      ?>
      
      <?php
        touring_kitekinto();
      ?>
     
      <?php
        touring_felsofooter();
      ?>
      
      <?php
        touring_alsofooter();
      ?>
      
      <?php
        touring_suti();
      ?>
    
    <?php print $js_section; ?>
    <script src="https://spark.engaga.com/public/site.js" id="engaga-script" data-engaga-user-id="2b38ca532a8518c0b974c820106378c6" async="async"></script>
    <?php print $GAcode; ?>
	
  </body>
</html>
