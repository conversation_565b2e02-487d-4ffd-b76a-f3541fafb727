<!DOCTYPE html>
<html lang='en'>
<head>
  <meta charset='utf-8'>
  <title>NProgress: slim progress bars in JavaScript</title>
  <link href='support/style.css' rel='stylesheet' />
  <link href='nprogress.css' rel='stylesheet' />

  <meta name="viewport" content="width=device-width">
  <link href='http://fonts.googleapis.com/css?family=Source+Sans+Pro:200,400,700,400italic' rel='stylesheet' type='text/css'>
  
  <script src="https://code.jquery.com/jquery-1.11.2.min.js"></script>
  <script src='nprogress.js'></script>

  <script>if(location.hostname.match(/ricostacruz\.com$/)){var _gaq=_gaq||[];_gaq.push(["_setAccount","UA-********-1"]),_gaq.push(["_trackPageview"]),function(){var a=document.createElement("script");a.type="text/javascript",a.async=!0,a.src=("https:"==document.location.protocol?"https://ssl":"http://www")+".google-analytics.com/ga.js";var b=document.getElementsByTagName("script")[0];b.parentNode.insertBefore(a,b)}()}</script>
</head>

<body style='display: none'>
  <header class='page-header'>
    <span class='nprogress-logo fade out'></span>
    <h1>NProgress<i>.js</i></h1>
    <p class='fade out brief big'>A nanoscopic progress bar. Featuring realistic
    trickle animations to convince your users that something is happening!</p>
  </header>

  <div class='contents fade out'>
    <div class='controls'>
      <p>
        <button class='button play' id='b-0'></button>
        <i>NProgress</i><b>.start()</b>
        &mdash;
        shows the progress bar
      </p>
      <p>
        <button class='button play' id='b-40'></button>
        <i>NProgress</i><b>.set(0.4)</b>
        &mdash;
        sets a percentage
      </p>
      <p>
        <button class='button play' id='b-inc'></button>
        <i>NProgress</i><b>.inc()</b>
        &mdash;
        increments by a little
      </p>
      <p>
        <button class='button play' id='b-100'></button>
        <i>NProgress</i><b>.done()</b>
        &mdash;
        completes the progress
      </p>
    </div>
    <div class='actions'>
      <a href='https://github.com/rstacruz/nprogress' class='button primary big'>
        Download
        v<span class='version'></span>
      </a>
      <p class='brief'>Perfect for Turbolinks, Pjax, and other Ajax-heavy apps.</p>
    </div>
    <div class='hr-rule'></div>
    <div class='share-buttons'>
      <iframe src="http://ghbtns.com/github-btn.html?user=rstacruz&repo=nprogress&type=watch&count=true"
          allowtransparency="true" frameborder="0" scrolling="0" width="100" height="20"></iframe>
      <iframe src="http://ghbtns.com/github-btn.html?user=rstacruz&type=follow&count=true"
          allowtransparency="true" frameborder="0" scrolling="0" width="175" height="20"></iframe>
      <a href="https://news.ycombinator.com/submit" class="hn-button" data-title="NProgress" data-url="http://ricostacruz.com/nprogress/" data-count="horizontal" data-style="twitter">HN</a>
    </div>
  </div>

  <script>
    $('body').show();
    $('.version').text(NProgress.version);
    NProgress.start();
    setTimeout(function() { NProgress.done(); $('.fade').removeClass('out'); }, 1000);

    $("#b-0").click(function() { NProgress.start(); });
    $("#b-40").click(function() { NProgress.set(0.4); });
    $("#b-inc").click(function() { NProgress.inc(); });
    $("#b-100").click(function() { NProgress.done(); });
  </script>

  <script>var HN=[];HN.factory=function(e){return function(){HN.push([e].concat(Array.prototype.slice.call(arguments,0)))};},HN.on=HN.factory("on"),HN.once=HN.factory("once"),HN.off=HN.factory("off"),HN.emit=HN.factory("emit"),HN.load=function(){var e="hn-button.js";if(document.getElementById(e))return;var t=document.createElement("script");t.id=e,t.src="//hn-button.herokuapp.com/hn-button.js";var n=document.getElementsByTagName("script")[0];n.parentNode.insertBefore(t,n)},HN.load();</script>
</body>
</html>
