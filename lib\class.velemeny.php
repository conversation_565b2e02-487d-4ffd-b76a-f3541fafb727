<?php

//touring vélemények myaaaaaah!!

class velemeny {
    
    private $id;
    private $nev;
    private $datum;
    private $szoveg_1;
    private $szoveg_2;
    private $szoveg_3;
    private $szoveg_4;
    private $aktiv;
    
    public function __construct($id) {
        $pdo = connect_pdo();
        $sql_selvelemeny = "SELECT * FROM velemeny WHERE velemeny_id = :id";
        $params_vel = array(':id'=>$id);
        $stmt_vel = $pdo->prepare($sql_selvelemeny);
        bindSqlPArams($stmt_vel, $params_vel);
        $ret_selvelemeny = $stmt_vel->fetchAll(PDO::FETCH_ASSOC);
        foreach ($ret_selvelemeny as $rvel) {
            $this->id = $id;
            $this->nev = $rvel['velemeny_nev'];
            $this->datum = $rvel['velemeny_datum'];
            $this->szoveg_1 = $rvel['velemeny_szoveg_1'];
            $this->szoveg_2 = $rvel['velemeny_szoveg_2'];
            $this->szoveg_3 = $rvel['velemeny_szoveg_3'];
            $this->szoveg_4 = $rvel['velemeny_szoveg_4'];
            $this->aktiv = $rvel['velemeny_aktiv'];
        }
    }
    
    //getterek
    
    function getId() {
        return $this->id;
    }
    
    function getNev() {
        return $this->nev;
    }
    
    function getDatum() {
        return $this->datum;
    }
    
    function getSzoveg1() {
        return $this->szoveg_1;
    }
    
    function getSzoveg2() {
        return $this->szoveg_2;
    }
    
    function getSzoveg3() {
        return $this->szoveg_3;
    }
    
    function getSzoveg4() {
        return $this->szoveg_4;
    }
    
    function getAktiv() {
        return $this->aktiv;
    }
    
    //setterek
    
    function setNev($nev) {
        $this->nev = $nev;
    }
    
    function setDatum($datum) {
        $this->datum = $datum;
    }
    
    function setSzoveg1($szoveg) {
        $this->szoveg_1 = $szoveg;
    }
    
    function setSzoveg2($szoveg) {
        $this->szoveg_2 = $szoveg;
    }
    
    function setSzoveg3($szoveg) {
        $this->szoveg_3 = $szoveg;
    }
    
    function setSzoveg4($szoveg) {
        $this->szoveg_4 = $szoveg;
    }
    
    function setAktiv($aktiv) {
        $this->aktiv = $aktiv;
    }
    
    function update() {
        $pdo = connect_pdo();
        $sql_updvel = "UPDATE velemeny SET velemeny_nev = :nev, velemeny_datum = :datum, velemeny_szoveg_1 = :szoveg_1, velemeny_szoveg_2 = :szoveg_2, velemeny_szoveg_3 = :szoveg_3, velemeny_szoveg_4 = :szoveg_4, velemeny_aktiv = :aktiv WHERE velemeny_id = :id";
        $params_vel = array(':nev'=>$this->nev, ':datum'=>$this->datum, ':szoveg_1'=>$this->szoveg_1, ':szoveg_2'=>$this->szoveg_2, ':szoveg_3'=>$this->szoveg_3, ':szoveg_4'=>$this->szoveg_4, ':aktiv'=>$this->aktiv, ':id'=>$this->id);
        $stmt_vel = $pdo->prepare($sql_updvel);
        
        if ( bindSqlPArams($stmt_vel, $params_vel) ) {
            return TRUE;
        } else {
            return FALSE;
        }
    }
}