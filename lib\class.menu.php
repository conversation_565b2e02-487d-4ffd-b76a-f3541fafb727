<?php

class menu {
    
    private $id;
    private $nev_1;
    private $nev_2;
    private $nev_3;
    private $nev_4;
    private $link;
    private $aktiv;
    
    public function __construct($id) {
        $pdo = connect_pdo();
        $sql_selmenu = "SELECT * FROM menu WHERE menu_id = :id";
        $params_menu = array(':id'=>$id);
        $stmt_menu = $pdo->prepare($sql_selmenu);
        bindSqlPArams($stmt_menu, $params_menu);
        $ret_selmenu = $stmt_menu->fetchAll(PDO::FETCH_ASSOC);
        foreach ($ret_selmenu as $rmenu) {
            $this->id = $id;
            $this->nev_1 = $rmenu['menu_nev_1'];
            $this->nev_2 = $rmenu['menu_nev_2'];
            $this->nev_3 = $rmenu['menu_nev_3'];
            $this->nev_4 = $rmenu['menu_nev_4'];
            $this->link = $rmenu['menu_link'];
            $this->aktiv = $rmenu['menu_aktiv'];
        }
    }
    
    //getterek
    
    function getId() {
        return $this->id;
    }
    
    function getNev1() {
        return $this->nev_1;
    }
    
    function getNev2() {
        return $this->nev_2;
    }
    
    function getNev3() {
        return $this->nev_3;
    }
    
    function getNev4() {
        return $this->nev_4;
    }
    
    function getLink() {
        return $this->link;
    }
    
    function getAktiv() {
        return $this->aktiv;
    }
    
    //setterek
    
    function setNev1($nev) {
        $this->nev_1 = $nev;
    }
    
    function setNev2($nev) {
        $this->nev_2 = $nev;
    }
    
    function setNev3($nev) {
        $this->nev_3 = $nev;
    }
    
    function setNev4($nev) {
        $this->nev_4 = $nev;
    }
    
    function setLink($link) {
        $this->link = $link;
    }
    
    function setAktiv($aktiv) {
        $this->aktiv = $aktiv;
    }
}