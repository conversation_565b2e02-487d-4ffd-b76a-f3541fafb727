<?php

class kitekinto {
    
    private $id;
    private $nev_1;
    private $nev_2;
    private $nev_3;
    private $nev_4;
    private $seo_key;
    private $seo_desc;
    private $kep;
    private $kezd_datum;
    private $veg_datum;
    private $desc_1;
    private $desc_2;
    private $desc_3;
    private $desc_4;
    private $aktiv;
    
    public function __construct($id) {
        $pdo = connect_pdo();
        $sql_selkit = "SELECT * FROM kitekinto WHERE kitekinto_id = :id";
        $params_kit = array(':id'=>$id);
        $stmt_kit = $pdo->prepare($sql_selkit);
        bindSqlPArams($stmt_kit, $params_kit);
        $ret_selkit = $stmt_kit->fetchAll(PDO::FETCH_ASSOC);
        foreach ($ret_selkit as $rkit) {
            $this->id = $id;
            $this->nev_1 = $rkit['kitekinto_nev_1'];
            $this->nev_2 = $rkit['kitekinto_nev_2'];
            $this->nev_3 = $rkit['kitekinto_nev_3'];
            $this->nev_4 = $rkit['kitekinto_nev_4'];
            $this->seo_key = $rkit['kitekinto_seo_key'];
            $this->seo_desc = $rkit['kitekinto_seo_desc'];
            $this->kep[] = $rkit['kitekinto_kep'];
            $this->kezd_datum = $rkit['kitekinto_kezd_datum'];
            $this->veg_datum = $rkit['kitekinto_veg_datum'];
            $this->desc_1 = $rkit['kitekinto_desc_1'];
            $this->desc_2 = $rkit['kitekinto_desc_2'];
            $this->desc_3 = $rkit['kitekinto_desc_3'];
            $this->desc_4 = $rkit['kitekinto_desc_4'];
            $this->aktiv = $rkit['kitekinto_aktiv'];
        }
    }
    
    //getterek
    
    function getId() {
        return $this->id;
    }
    
    function getNev1() {
        return $this->nev_1;
    }
    
    function getNev2() {
        return $this->nev_2;
    }
    
    function getNev3() {
        return $this->nev_3;
    }
    
    function getNev4() {
        return $this->nev_4;
    }
    
    function getSeoKey() {
        return $this->seo_key;
    }
    
    function getSeoDesc() {
        return $this->seo_desc;
    }
            
    function getKep() {
        return $this->kep;
    }
    
    function getKezdDatum() {
        return $this->kezd_datum;
    }
    
    function getVegDatum() {
        return $this->veg_datum;
    }
            
    function getDesc1() {
        return $this->desc_1;
    }
    
    function getDesc2() {
        return $this->desc_2;
    }
    
    function getDesc3() {
        return $this->desc_3;
    }
    
    function getDesc4() {
        return $this->desc_4;
    }
    
    function getAktiv() {
        return $this->aktiv;
    }
    
    //setterek
    
    function setNev1($nev) {
        $this->nev_1 = $nev;
    }
    
    function setNev2($nev) {
        $this->nev_2 = $nev;
    }
    
    function setNev3($nev) {
        $this->nev_3 = $nev;
    }
    
    function setNev4($nev) {
        $this->nev_4 = $nev;
    }
    
    function setSeoKey($seo_key) {
        $this->seo_key = $seo_key;
    }
    
    function setSeoDesc($seo_desc) {
        $this->seo_desc = $seo_desc;
    }
            
    function setKep($kep) {
        $this->kep = $kep;        
    }
    
    function setKezdDatum($datum) {
        $this->kezd_datum = $datum;
    }
    
    function setVegDatum($datum) {
        $this->veg_datum = $datum;
    }
            
    function setDesc1($desc) {
        $this->desc_1 = $desc;
    }
    
    function setDesc2($desc) {
        $this->desc_2 = $desc;
    }
    
    function setDesc3($desc) {
        $this->desc_3 = $desc;
    }
    
    function setDesc4($desc) {
        $this->desc_4 = $desc;
    }
    
    function setAktiv($aktiv) {
        $this->aktiv = $aktiv;
    }
    
    function update() {
        $pdo = connect_pdo();
        $kep = $this->kep[0];        
        $sql_updkit = "UPDATE kitekinto SET kitekinto_nev_1 = :nev_1, kitekinto_nev_2 = :nev_2, kitekinto_nev_3 = :nev_3, kitekinto_nev_4 = :nev_4, kitekinto_seo_key = :seo_key, kitekinto_seo_desc = :seo_desc, kitekinto_kep = :kep, kitekinto_kezd_datum = :kezd_datum, kitekinto_veg_datum = :veg_datum, kitekinto_desc_1 = :desc_1, kitekinto_desc_2 = :desc_2, kitekinto_desc_3 = :desc_3, kitekinto_desc_4 = :desc_4, kitekinto_aktiv = :aktiv WHERE kitekinto_id = :id";
        $params_kit = array(':nev_1'=>$this->nev_1, ':nev_2'=>$this->nev_2, ':nev_3'=>$this->nev_3, ':nev_4'=>$this->nev_4, ':seo_key'=>$this->seo_key, ':seo_desc'=>$this->seo_desc, ':kep'=>$kep, ':kezd_datum'=>$this->kezd_datum, ':veg_datum'=>$this->veg_datum, ':desc_1'=>$this->desc_1, ':desc_2'=>$this->desc_2, ':desc_3'=>$this->desc_3, ':desc_4'=>$this->desc_4, ':aktiv'=>$this->aktiv, ':id'=>$this->id);
        $stmt_kit = $pdo->prepare($sql_updkit);
        
        if ( bindSqlPArams($stmt_kit, $params_kit) ) {
            return TRUE;
        } else {
            return FALSE;
        }
    }
    
    function printKitekinto() {
        print '<div class="col-xs-12 col-sm-12 col-md-12 col-lg-6" style="margin-bottom:25px;">
                  <div class="ajanlatkep" style="height:300px">';
                  if ( @GetImageSize($_SERVER["DOCUMENT_ROOT"]."/kitekintokep/".$this->getKep()[0]) && $this->getKep()[0] != "" ) {
                    print '<a href="/kitekintokep/'.$this->getKep()[0].'" data-lightbox="'.$this->getKep()[0].'" data-title=""><img src="/kitekintokep/'.$this->getKep()[0].'" alt="kep1" /></a>';
                  } else {
                    print '<img src="/uploads/kep_hamarosan.jpg" alt="kep1" />';
                  }
           print '</div>';
           print '<div class="ajanlatszoveg" style="height:300px;">
                      <div class="row">
                          <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 ajanlatfejlec">
                              
                              <span class="ajanlatnev">';
                                    switch ($GLOBALS['lang']) {
                                        case '1':
                                            print $this->getNev1();
                                            break;
                                        case '2':
                                            print $this->getNev2();
                                            break;
                                        case '3':
                                            print $this->getNev3();
                                            break;
                                        case '4':
                                            print $this->getNev4();
                                            break;
                                        default:
                                            print $this->getNev1();
                                            break;
                                    }
                       print '</span><br />
                              
                          </div>
                
                      </div>
  
                      <div class="row">
                          <div class="col-xs-10 col-sm-10 col-md-10 col-lg-10 col-xs-offset-1 col-sm-offset-1 col-md-offset-1 col-lg-offset-1 ajanlatreszlet">';
                            switch ($GLOBALS['lang']) {
                                case '1':
                                    print substr($this->getDesc1(),0,300);
                                    break;
                                case '2':
                                    print substr($this->getDesc2(),0,300);
                                    break;
                                case '3':
                                    print substr($this->getDesc3(),0,300);
                                    break;
                                case '4':
                                    print substr($this->getDesc4(),0,300);
                                    break;
                                default:
                                    print substr($this->getDesc1(),0,300);
                                    break;
                            }
                   print '</div>
                         
                         <div class="col-xs-4 col-sm-4 col-md-4 col-lg-4 col-xs-offset-4 col-sm-offset-4 col-md-offset-4 col-lg-offset-4">
                              <a href="kitekinto.php?id='.$this->getId().'" class="ajanlatgomb-szabad">'.$GLOBALS['_lefoglalom_gomb'].'</a>
                          </div>
                      </div>
                      
                  </div>
             </div>';
    }
}