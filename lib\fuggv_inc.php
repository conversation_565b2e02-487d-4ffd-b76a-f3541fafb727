<?php

/*==============================================================================
#    
#  TurIr rendszer configurációs állomány és függvény könyvtár
#  Készítette:      Armametna Bt. www.armacomp.hu
#  Programozó:      Éliás Attila
#  Verzió:          0.1
#  Karakterkódolás: UTF-8
#  
#=============================================================================*/

/*---- Minden alkalommal lefutó események ----*/
//session_set_cookie_params(86400,"/");
error_reporting(E_ERROR);
ini_set('display_errors', TRUE);
ini_set('display_startup_errors', TRUE);
session_start(); //Elindítja a munkamenetet

 /*$_GET = array_map('addslashes', $_GET); 
 $_POST = array_map('addslashes', $_POST); 
 $_COOKIE = array_map('addslashes', $_COOKIE); 
 $_REQUEST = array_map('addslashes', $_REQUEST); //Kis trükk a tömbökkel, mysql_inject kivédés*/
 
 //$_POST = array_map('addslashes', $_POST); 
 
 global $ErrMsg; //hibaüzenetek tárolója
 $ErrMsg = '';
 global $OkMsg; //rendben üzenetek tárolója
 $OkMsg = '';
 
 
 $konyvtar_galkep = $_SERVER["DOCUMENT_ROOT"]."/gal/";
 $konyvtar_szobakep = $_SERVER["DOCUMENT_ROOT"]."/szobakep/";
 $konyvtar_etelkep = $_SERVER["DOCUMENT_ROOT"]."/etelkep/";
 $konyvtar_rendkep = $_SERVER["DOCUMENT_ROOT"]."/rendkep/";
 $konyvtar_sliders = $_SERVER["DOCUMENT_ROOT"]."/sliders/";
 $konyvtar_csomagkep = $_SERVER["DOCUMENT_ROOT"]."/csomagkep/";
 $konyvtar_kitekintokep = $_SERVER["DOCUMENT_ROOT"]."/kitekintokep/";
 
 
 //include 'language/hu.php'; //nyelvi file izé
 include_once 'class.szoba.php';
 include_once 'class.etel.php';
 include_once 'class.rendezveny.php';
 include_once 'class.ajanlat.php';
 include_once 'class.page.php';
 include_once 'class.velemeny.php';
 include_once 'class.sliders.php';
 include_once 'class.menu.php';
 include_once 'class.csomag.php';
 include_once 'class.kitekinto.php';
 
  switch ($_COOKIE ['lang']) {
 case 'hu':
  $lang = '1';
  include $_SERVER["DOCUMENT_ROOT"].'/language/hu.php';
  break;
 case 'en':
  $lang = '2';
  include $_SERVER["DOCUMENT_ROOT"].'/language/en.php';
  break;
 case 'de':
  $lang = '3';
  include $_SERVER["DOCUMENT_ROOT"].'/language/de.php';
  break;
 case 'sk':
  $lang = '4';
  include $_SERVER["DOCUMENT_ROOT"].'/language/sk.php';
  break;
 default:
  $lang = '1';
  include $_SERVER["DOCUMENT_ROOT"].'/language/hu.php';
  break;
 }

function checklang() {
 switch ($_COOKIE ['lang']) {
  case 'hu':
  return 'hu';
 case 'en':
  return 'en';
 case 'de':
  return 'de';
 case 'sk':
  return 'sk';
 default:
  return 'hu';
}
}

/*

 function connect() {
  $co_host = "localhost" ; // sql adatbázis cím
  $co_name = "touringhot_web" ; // adatbázis felhasználónév
  $co_pw = "V4AezmZnZ4yxP8wRqKrw" ; // adatbázis jelszó
  $co_db = "touringhot_web" ; // adatbázis neve
 
  mysql_connect ( $co_host , $co_name , $co_pw ) or die( mysql_error ());
  mysql_select_db ( $co_db ) or die( mysql_error ());
  
  mysql_query ("SET NAMES UTF8"); //karakterkódolási mizériák miatt
 }
 */

 /*PDO tools*/

function connect_pdo() {
    $dsn = 'mysql:host=localhost;dbname=touringhot_web';
    $username = 'touringhot_web';
    $password = "V4AezmZnZ4yxP8wRqKrw";
    try {
        $pdo = new PDO($dsn, $username, $password);
        $pdo->exec("Set names utf8");
        $pdo->exec("set session sql_mode=replace(@@sql_mode,'STRICT_TRANS_TABLES','')");
    } catch (PDOException $e) {
        echo 'Connection failed: ' . $e->getMessage();
    }
    return $pdo;
}

function bindSqlPArams($sql_obj, $params = array(), $stringonly = false) {
    foreach ($params as $key => $value) {
        if ( $stringonly ) {
            $sql_obj->bindValue($key, $value);
        } else {
            if ( is_numeric($value) ) {
                $sql_obj->bindValue($key, $value, PDO::PARAM_INT);
            } else {
                $sql_obj->bindValue($key, $value);
            }
        }
    }
    if ($sql_obj->execute()) {
        return true;
    } else {
        return false;
    }
}

function debugMySql($sql_obj) {
    print '<pre>';
    var_dump($sql_obj->queryString);
    print '<hr />';
    var_dump($sql_obj->debugDumpParams());
    print '</pre>';
}
 
 /*function classLoader() {/**Saját osztályokat importál**/
  /*connect();
  $sql_selclasses = "Select * From autoloader Where autoload_aktiv = 1 Order By autoload_id ASC";
  $ret_selclasses = mysql_query ($sql_selclasses);
  while ($sclass = mysql_fetch_array ($ret_selclasses)) {
   include_once $sclass ['autoload_filename'];
  }
 }
 classLoader();*/
 
 /*include_once 'class.subMenu.php';
 include_once 'class.listExporter.php';
 include_once 'class.listImporter.php';
 include_once 'class.errorMessage.php';*/

$lightbox = '<link href="css/lightbox.css" rel="stylesheet" />
<script src="js/lightbox.js"></script>';
 
 /*---- Login form kiíratás ----*/
 function printlogin($ErrMsg) {
  $lform = '
  <div class="container">
     <div class="col-md-4">
      
     </div>
     <div class="col-md-4">
      <h1>LBR Belépés</h1>
      <form method="post" action="index.php">
       <div class="input-group">
        <span class="input-group-addon"><i class="glyphicon glyphicon-user"></i></span>
        <input id="email" type="text" class="form-control" name="email" placeholder="E-mail">
       </div>
       <div class="input-group">
        <span class="input-group-addon"><i class="glyphicon glyphicon-lock"></i></span>
        <input id="password" type="password" class="form-control" name="pass" placeholder="Jelszó">
       </div>
       <button type="submit" class="btn btn-primary btn-block" name="lsub">Belép</button>
      </form>
     </div>
     <div class="col-md-4">
      
     </div>
    </div>
	';
	print $lform;
 }
 
/*---- Beléptetés ----*/
function login() {
    $pdo = connect_pdo();

    $email = strtolower(mysql_real_escape_string(htmlspecialchars($_POST ['email'])));
	$pass = mysql_real_escape_string(htmlspecialchars($_POST ['pass']));
	$lsub = $_POST ['lsub'];
	$ErrMsg;
	
	if ( $email != '' ) {
        $sql_getuser = "Select * From user Where LOWER(user_mail) = :email";
        $stmt_login = $pdo->prepare($sql_getuser);
        $stmt_login->execute(['email' => $email]);
        $users = $stmt_login->fetchAll(PDO::FETCH_ASSOC);
        $lgnum = count($users);
        if ( $lgnum == 1 ) {
            foreach ($users as $getuser) {
                if ( $pass == $getuser ['user_pass'] ) {
                    //$_SESSION [''] = '';
                    $_SESSION ['mail'] = $email;
                    $_SESSION ['user'] = $getuser ['user_nev'];
                    $_SESSION ['user_id'] = $getuser ['user_id'];
                    $_SESSION ['logged'] = 1;
                    header('Location: belso.php');
                } else {
                    //print 'Hiba 1';
                    $ErrMsg = $GLOBALS ['_hibas_elepesi_adat'];
                    printlogin($ErrMsg);
                }
            }
        } else {
            //print 'hiba 2';
            $ErrMsg = $GLOBALS ['_nincs_ilyen_user'];
            printlogin($ErrMsg);
        }
    } else {
        //print 'hiba 3';
        printlogin($ErrMsg);
    }
}
 
function logged() {
    if ( isset($_SESSION ['logged']) and $_SESSION ['logged'] == 1 ) {
        return true; 
    } else {
        header('Location: index.php');
    }
}
 
function getCsoport() {
    //$_SESSION ['csoport']
    return $_SESSION ['csoport'];
}
 
function ifadmin() {
    //$_SESSION ['csoport']
    if ( $_SESSION ['jogkor'] == 1 ) {
        return true;
    } else {
        return false;
    }
}
 
/*---- menük ----*/

function isSzuper() {
    $pdo = connect_pdo();
    $csopid = $_SESSION ['csoport'];
    $mindenjog = false;
    
    $sql_selszuper = "Select csoport_mindenjog From csoport Where csoport_id = :csopid";
    $params = array(':csopid'=>$csopid);
    $stmt = $pdo->prepare($sql_selszuper);
    bindSqlPArams($stmt, $params);
    $ret_selszuper = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($ret_selszuper as $sps) {
        if ( $sps ['csoport_mindenjog'] == 1 ) {
            return true;
        } else {
            return false;
        }
    }
}

function mainmenu() {
 
 print '<ul class="nav side-menu">
          <li>
            <a><i class="fa fa-home"></i> Szöveges aloldalak <span class="fa fa-chevron-down"></span></a>
                          <ul class="nav child_menu">
                              <li><a href="admin_hotel.php" class="norm" title="Hotel adminisztráció">Hotel</a></li> 
                              <li><a href="admin_jogi_nyilatkozat.php" class="norm" title="Jogi nyilatkozat adminisztráció">Jogi nyilatkozat</a></li>
                              <li><a href="admin_kapcsolat.php" class="norm" title="Kapcsolat adminisztráció">Kapcsolat</a></li>                          
              </ul>
            </li>'; //szöveges aloldalak, étterem, sliderek, árajánlatkérések
  
 
  print '<li>
            <a><i class="fa fa-home"></i> Galéria adminisztráció <span class="fa fa-chevron-down"></span></a>
                          <ul class="nav child_menu">'
                            . '<li><a href="admin_fotok.php" class="norm" title="Fotók adminisztráció">Fotók</a></li>'
                          .'</ul>'
          . '</li>';
  
  print '<li>
            <a><i class="fa fa-home"></i> Szoba adminisztráció <span class="fa fa-chevron-down"></span></a>
                          <ul class="nav child_menu">'
                            . '<li><a href="admin_szobak.php" class="norm" title="Szobák adminisztráció">Szobák</a></li>'
                          .'</ul>'
          . '</li>';
  
  print '<li>
            <a><i class="fa fa-home"></i> Étel adminisztráció <span class="fa fa-chevron-down"></span></a>
                          <ul class="nav child_menu">'
                            . '<li><a href="admin_etel.php" class="norm" title="Étel adminisztráció">Étel</a></li>'
                          .'</ul>'
          . '</li>';
  
  print '<li>
            <a><i class="fa fa-home"></i> Rendezvény adminisztráció <span class="fa fa-chevron-down"></span></a>
                          <ul class="nav child_menu">'
                            . '<li><a href="admin_rendezveny.php" class="norm" title="Rendezvény adminisztráció">Rendezvény</a></li>'
                          .'</ul>'
          . '</li>';
  
  print '<li>
            <a><i class="fa fa-home"></i> Ajánlatkérések adminisztráció <span class="fa fa-chevron-down"></span></a>
                          <ul class="nav child_menu">'
                            . '<li><a href="admin_ajanlat.php" class="norm" title="Ajánlatkérés adminisztráció">Ajánlatkérések</a></li>'
                          .'</ul>'
          . '</li>';
  
  print '<li>
            <a><i class="fa fa-home"></i> Vélemények adminisztráció <span class="fa fa-chevron-down"></span></a>
                          <ul class="nav child_menu">'
                            . '<li><a href="admin_velemeny.php" class="norm" title="Vélemények adminisztráció">Vélemények</a></li>'
                          .'</ul>'
          . '</li>';
  
  print '<li>
            <a><i class="fa fa-home"></i> Sliderek adminisztráció <span class="fa fa-chevron-down"></span></a>
                          <ul class="nav child_menu">'
                            . '<li><a href="admin_sliders.php" class="norm" title="Sliderek adminisztráció">Sliderek</a></li>'
                          .'</ul>'
          . '</li>';
          
  print '<li>
            <a><i class="fa fa-home"></i> Árajánlat kérések <span class="fa fa-chevron-down"></span></a>
                          <ul class="nav child_menu">
                            <li><a href="belso.php?mm=1">Kezdőlap</a></li>
                                         <li><a href="megrendeles_uj.php?mm=1&amp;sm=42" class="norm" title="Új rendelés">Új rendelés</a></li>
                                         <li><a href="foraktar.php?mm=1&amp;sm=78" class="norm" title="Főraktár">Főraktár</a></li>
                                         <li><a href="kulsomegrend.php?mm=1&amp;sm=80" class="norm" title="Rendelés">Rendelés</a></li>
                                         <li><a href="szamlak.php?mm=1&amp;sm=87" class="norm" title="Számlák">Számlák</a></li>
                                         <li><a href="cikkek_kereskedo.php?mm=1&amp;sm=97" class="norm" title="Cikkek ker.">Cikkek ker.</a></li>
                                         <li><a href="kosar.php?mm=1&amp;sm=114" class="norm" title="Kosarak">Kosarak</a></li>
                                         <li><a href="rendelesvalogat.php?mm=1&amp;sm=115" class="norm" title="Rend. válogat">Rend. válogat</a></li>
                                         <li><a href="cikkaktivalo.php?mm=1&amp;sm=182" class="norm" title="Aktivalo">Aktivalo</a></li>
                                         <li><a>PTG funkciók<span class="fa fa-chevron-down"></span></a>
                                         <ul class="nav child_menu">
                                           <li><a href="aktiv_ptg.php?mm=1&amp;sm=240">Aktív ptgk</a></li>
                                                         <li><a href="felulvizsg_ptg.php?mm=1&amp;sm=241">Felülvizsg.</a></li>
                                                         <li><a href="felulvizsgalt_ptgk.php?mm=1&amp;sm=274">Felülvizsgált Lista</a></li>
                                                         <li><a href="felulvizsgaltak_export_micra.php?mm=1&amp;sm=279">Mic./Mont. felülv.</a></li>
                                         </ul>
                                         </li>
                                         <li><a>Leltár funkc.<span class="fa fa-chevron-down"></span></a>
                                                         <ul class="nav child_menu">
                                                         <li><a href="bevmain_import.php?mm=1&amp;sm=347">BevMain import</a></li>
                                                         <li><a href="bevmain_temp.php?mm=1&amp;sm=349">BevMain tábla</a></li>
                                                         </ul>
                                         </li>
                                         </ul>
      </li>';
}

function picupload($location, $location_rel, $nagyszel, $kisszel = 0, $kismagas = 0) {
//sample: $array = picupload('/var/www/htdocs/keptar/', 'keptar/', '500', '100', '60');
 
 if ( $nagyszel == '' or $nagyszel == 0 ) {
  $nagyszel = 500;
 }
 
 $outpic = '';
 foreach($_FILES as $allomanynev => $all_tomb) { 
	if (is_uploaded_file($all_tomb['tmp_name'])) {
	if ( ( strstr($all_tomb['type'], 'jpeg') or strstr($all_tomb['type'], 'png') or strstr($all_tomb['type'], 'gif') ) ) {
		$all_tomb['name'] = str_replace('é', 'e', $all_tomb['name']);
           $all_tomb['name'] = str_replace('á', 'a', $all_tomb['name']);
           $all_tomb['name'] = str_replace('í', 'i', $all_tomb['name']);
           $all_tomb['name'] = str_replace('ű', 'u', $all_tomb['name']);
           $all_tomb['name'] = str_replace('ü', 'u', $all_tomb['name']);
           $all_tomb['name'] = str_replace('ú', 'u', $all_tomb['name']);
           $all_tomb['name'] = str_replace('ő', 'o', $all_tomb['name']);
           $all_tomb['name'] = str_replace('ö', 'o', $all_tomb['name']);
           $all_tomb['name'] = str_replace('ó', 'o', $all_tomb['name']);
           $all_tomb['name'] = str_replace('É', 'e', $all_tomb['name']);
           $all_tomb['name'] = str_replace('Á', 'a', $all_tomb['name']);
           $all_tomb['name'] = str_replace('Í', 'i', $all_tomb['name']);
           $all_tomb['name'] = str_replace('Ű', 'u', $all_tomb['name']);
           $all_tomb['name'] = str_replace('Ü', 'u', $all_tomb['name']);
           $all_tomb['name'] = str_replace('Ú', 'u', $all_tomb['name']);
           $all_tomb['name'] = str_replace('Ő', 'o', $all_tomb['name']);
           $all_tomb['name'] = str_replace('Ö', 'o', $all_tomb['name']);
           $all_tomb['name'] = str_replace('Ó', 'o', $all_tomb['name']);
           $all_tomb['name'] = str_replace(' ', '_', $all_tomb['name']);
           $all_tomb['name'] = str_replace('%', '_', $all_tomb['name']);           
           $all_tomb['name'] = str_replace(' ', '_', $all_tomb['name']);
           $all_tomb['name'] = str_replace("\'", '_', $all_tomb['name']);
           $all_tomb['name'] = str_replace(":", '_', $all_tomb['name']);
           //print $all_tomb['name']; 
    move_uploaded_file($all_tomb['tmp_name'], $location.$all_tomb['name']) or die ("ERROR!");
 		chmod($location.$cikkszam.''.$ido.$all_tomb['name'], 0777);
                //print $location.$cikkszam.'_'.$ido.$all_tomb['name'];
     $outpic [] = $all_tomb['name'];
     $op = $all_tomb['name'];
            if ( strstr($all_tomb['type'], 'jpeg') || strstr($all_tomb['type'], 'jpg') ) {

             $im2 = ImageCreateFromJpeg($location.$op);
             list($width2, $height2) = getimagesize($location.$op);
             
             $new_width2 = $nagyszel;
             $new_height2 = ( $new_width2 / $width2 ) * $height2;
             
             $image_p2 = imagecreatetruecolor($new_width2, $new_height2);
             imagecopyresampled($image_p2, $im2, 0, 0, 0, 0, $new_width2, $new_height2, $width2, $height2);
             $filename2 = $location.$op;
             imagejpeg($image_p2,$filename2,80);
             imagedestroy($im2);
             imagedestroy($image_p2);
             chmod($location.$op, 0777);

             if ( $kisszel != '' or $kisszel != 0 ) {
						 $im = ImageCreateFromJpeg($location.$op);
             list($width, $height) = getimagesize($location.$op);
             
             $new_width = $kisszel;
             $new_height = ( $new_width / $width ) * $height;
             if ($new_height > $kismagas) {
              $new_width = $kismagas; 
             }
             $image_p = imagecreatetruecolor($new_width, $new_height);
             imagecopyresampled($image_p, $im, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
             $filename = $location."tn_".$op;
             imagejpeg($image_p,$filename,70);
             imagedestroy($im);
             imagedestroy($image_p);
             chmod($location.'tn_'.$op, 0777); 
            }
            }
            
            if ( strstr($all_tomb['type'], 'gif') ) {
             if ( $kisszel != '' or $kisszel != 0 ) {
             $im = ImageCreateFromGif($location.$op);
             list($width, $height) = getimagesize($location.$op);
             $new_width = $kisszel;
             $new_height = ( $new_width / $width ) * $height;
             if ($new_height > $kismagas) {
              $new_width = $kismagas; 
             }
             $image_p = imagecreatetruecolor($new_width, $new_height);
             imagecopyresampled($image_p, $im, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
             $filename = $location."tn_".$op;
             imagegif($image_p,$filename,100);
             imagedestroy($im);
             imagedestroy($image_p);
             chmod($location.'tn_'.$op, 0777);
             }
             $im2 = ImageCreateFromGif($location.$op);
             list($width2, $height2) = getimagesize($location.$op);
             
             $new_width2 = 500;
             $new_height2 = ( $new_width2 / $width2 ) * $height2;
             
             $image_p2 = imagecreatetruecolor($new_width2, $new_height2);
             imagecopyresampled($image_p2, $im2, 0, 0, 0, 0, $new_width2, $new_height2, $width2, $height2);
             $filename2 = $location.$op;
             imagegif($image_p2,$filename2,100);
             imagedestroy($im2);
             imagedestroy($image_p2);
             chmod($location.$op, 0777);
              
            }
            if ( strstr($all_tomb['type'], 'png') ) {
             if ( $kisszel != '' or $kisszel != 0 ) {
             $im = ImageCreateFromPng($location.$op);
             list($width, $height) = getimagesize($location.$op);
             $new_width = $kisszel;
             $new_height = ( $new_width / $width ) * $height;
             if ($new_height > $kismagas) {
              $new_width = $kismagas; 
             }
             $image_p = imagecreatetruecolor($new_width, $new_height);
             imagecopyresampled($image_p, $im, 0, 0, 0, 0, $new_width, $new_height, $width, $height);
             $filename = $location."tn_".$op;
             imagepng($image_p,$filename);
             imagedestroy($im);
             imagedestroy($image_p);
             chmod($location.'tn_'.$op, 0777);
             }
             $im2 = ImageCreateFromPng($location.$kep);
             list($width2, $height2) = getimagesize($location.$op);
             
             $new_width2 = 500;
             $new_height2 = ( $new_width2 / $width2 ) * $height2;
             
             $image_p2 = imagecreatetruecolor($new_width2, $new_height2);
             $filename2 = $location.$op;
             imagepng($image_p2,$filename2);
             imagedestroy($im2);
             imagedestroy($image_p2);
             chmod($location.$op, 0777);
            }
            
            
   } else {
    exit ('Nem megfelelő fileformátumot válaszottál!');
   }
	}
}
return $outpic;
}

function clearLineBreak($text) {
    $text = preg_replace(array("/\r\n\r\n/", "/\n\n/", "/\r\n/"), array("", ""), $text);
    $text = str_replace("\r", "", $text);
    $text = str_replace("\n", "", $text);
    return $text;
}
 
function hetnapjai($nap) {
    switch ($nap) {
        case 'Sunday':
            return 'Vasárnap';
            break;
        case 'Monday':
            return 'Hétfő';
            break;
        case 'Tuesday':
            return 'Kedd';
            break;
        case 'Wednesday':
            return 'Szerda';
            break;
        case 'Thursday':
            return 'Csütörtök';
            break;
        case 'Friday':
            return 'Péntek';
            break;
        case 'Saturday':
            return 'Szombat';
            break;
    }
}

function clearPdfChar($imp) {
    $inp = str_replace('í', 'i', $imp);
    $inp = str_replace('ű', 'ü', $imp);
    $imp = str_replace('ő', 'ö', $imp);
    $imp = str_replace('Ű', 'Ü', $imp);
    $imp = str_replace('Ő', 'Ö', $imp);
    return $imp;
}
 
function fileUploader($folder) {
    $location = '/var/www/'.$folder.'/';
    if ( !is_dir($location) ) {
        //mkdir($location, 0777);
    }
    foreach($_FILES as $allomanynev => $all_tomb) { 
        if (is_uploaded_file($all_tomb['tmp_name'])) {
            move_uploaded_file($all_tomb['tmp_name'], $location.$all_tomb['name']) or die ("ERROR!");
            //chmod($location.$all_tomb['name'], 0777);
            return $all_tomb['name'];
        }
    }
}

function clearCharForforms($arg) {
    $arg = str_replace('é', 'e', $arg);
    $arg = str_replace('á', 'a', $arg);
    $arg = str_replace('í', 'i', $arg);
    $arg = str_replace('ű', 'u', $arg);
    $arg = str_replace('ü', 'u', $arg);
    $arg = str_replace('ú', 'u', $arg);
    $arg = str_replace('ő', 'o', $arg);
    $arg = str_replace('ö', 'o', $arg);
    $arg = str_replace('ó', 'o', $arg);
    $arg = str_replace('É', 'e', $arg);
    $arg = str_replace('Á', 'a', $arg);
    $arg = str_replace('Í', 'i', $arg);
    $arg = str_replace('Ű', 'u', $arg);
    $arg = str_replace('Ü', 'u', $arg);
    $arg = str_replace('Ú', 'u', $arg);
    $arg = str_replace('Ő', 'o', $arg);
    $arg = str_replace('Ö', 'o', $arg);
    $arg = str_replace('Ó', 'o', $arg);
    $arg = str_replace(' ', '_', $arg);
    $arg = str_replace('%', '_', $arg);           
    $arg = str_replace(' ', '_', $arg);
    $arg = str_replace("\'", '_', $arg);
    $arg = str_replace(":", '_', $arg);
    $arg = str_replace("/", '_', $arg);
    $arg = str_replace('"', '_', $arg);
    $arg = str_replace(',', '', $arg);
    return $arg;
}

function slider() {
    $pdo = connect_pdo();
    $sql_selslider = "SELECT * FROM sliders WHERE sliders_aktiv != '0'";
    $params = array();
    $stmt = $pdo->prepare($$sql_selslider);
    bindSqlPArams($stmt, $params);
    $ret_selslider = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($ret_selslider as $sl) {
        print '<div class="sld">';
        if ( $sl['sliders_url'] != '' ) {
            print '<a href="'.$sl['sliders_url'].'" target="_blank">';
        }
        if ( $sl['sliders_focim_1'] != '' ) {
            print '<h1>'.$sl['sliders_focim_1'].'</h1>';
        }
        if ( $sl['sliders_alcim_1'] != '' ) {
            print '<h4>'.$sl['sliders_alcim_1'].'</h4>'; 
        }
        print '<img src="sliders/'.$sl['sliders_kep'].'" class="img-responsive" alt="'.$sl ['sliders_focim_1'].'" title="'.$sl ['sliders_focim_1'].'" class="img-responsive" />';
        if ( $sl['sliders_url'] != '' ) {
            print '</a>';
        }
        print '</div><!-- sld -->';
    }
}

$css_section = '
  <!-- bootstrap -->
  <link href="css/bootstrap.min.css" rel="stylesheet" media="screen">
  <link href="css/bootstrap-slider.css" rel="stylesheet" media="screen">
  <link href="css/font-awesome.min.css" rel="stylesheet">
  <link href="fonts/icon-7-stroke/css/pe-icon-7-stroke.css" rel="stylesheet">
  <link href="css/animate.css" rel="stylesheet" media="screen">
  <link href="css/owl.theme.css" rel="stylesheet">
  <link href="css/owl.carousel.css" rel="stylesheet">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.0.12/css/all.css" integrity="sha384-G0fIWCsCzJIMAVNQPfjH08cyYaUtMwjJwqiRKxxE/rx96Uroj1BtIQ6MLJuheaO9" crossorigin="anonymous">
  <!-- bootstrap -->
  <link type="text/css" href="css/media.css" rel="stylesheet" />
'.$lightbox;

 $js_section = '
<link rel="stylesheet" href="//code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
  <link rel="stylesheet" href="/resources/demos/style.css">
  <script src="https://code.jquery.com/jquery-1.12.4.js"></script>
  <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
  <script>
        $( function() {
          $( "#kezd_datum" ).datepicker({
                dateFormat:"yy-mm-dd",
                minDate:0
            });
        } );
        
                $("#kezd_datum_label").click(function() {
                  $("#kezd_datum").focus();
                });
        </script>
  
        <script>
        $( function() {
          $( "#veg_datum" ).datepicker({
                dateFormat:"yy-mm-dd",
                minDate: "+1d",
                startDate: "+1d"
            });
        } );
        
                $("#veg_datum_label").click(function() {
                  $("#veg_datum").focus();
                });
        </script>
        
        <script>
            function datumjs(id) {
                var datum = document.getElementById(id).value;
                var newdatum = datum.split("-");
                
                var month = newdatum[1];
                
                switch (month) {
                    case "01":
                        month = "JAN";
                        break;
                    case "02":
                        month = "FEB";
                        break;
                    case "03":
                        month = "MAR";
                        break;
                    case "04":
                        month = "APR";
                        break;
                    case "05":
                        month = "MAJ";
                        break;
                    case "06":
                        month = "JUN";
                        break;
                    case "07":
                        month = "JUL";
                        break;
                    case "08":
                        month = "AUG";
                        break;
                    case "09":
                        month = "SEP";
                        break;
                    case "10":
                        month = "OKT";
                        break;
                    case "11":
                        month = "NOV";
                        break;
                    case "12":
                        month = "DEC";
                        break;
                }

                document.getElementById(id+"_label").innerHTML = "<span class=\'foglalas-ajanlatkeres-szam\'>"+newdatum[2]+"</span>/"+month;
            }
        </script>

<!-- javascript fajlok -->
<script src="js/lib.js"></script>
<!--<script src="js/jquery.js"></script>-->
<script src="js/custom.js"></script>
<script src="js/bootstrap.min.js"></script>
<script src="js/bootstrap-slider.js"></script>
<script src="js/jquery.sticky.js"></script>
<script src="js/wow.min.js"></script>
<script src="js/owl.carousel.min.js"></script>
<script src="js/bootbox.min.js"></script>
<script type="text/javascript">
  $(".owl-carousel").owlCarousel({
  slideSpeed : 200,
  paginationSpeed : 1000,
  autoPlay : 5000,
  goToFirst : true,
  goToFirstSpeed : 1000,
  stopOnHover: true,
  lazyLoad: true,
  navigation : false,
  navigationText : ["prev","next"],
  pagination : true,
  paginationNumbers: false,
  responsive: true,
  items : 1,
  itemsDesktop : [1199,1],
  itemsDesktopSmall : [980,1],
  itemsTablet: [768,1],
  itemsMobile : [479,1]
  })
  
  $(".tp").owlCarousel({
  loop:true,
                slideSpeed : 200,
  paginationSpeed : 1000,
  autoPlay : 5000,
  goToFirst : true,
  goToFirstSpeed : 1000,
  stopOnHover: true,
  lazyLoad: true,
  autoWidth:true,
  navigation : false,
  navigationText : ["prev","next"],
  pagination : false,
  paginationNumbers: false,
  responsive: true,
  items : 6,
  itemsDesktop : [1169,6],
  itemsDesktopSmall : [980,4],
  itemsTablet: [768,4],
  itemsMobile : [479,2]
  })
  
  owl = $(".tp").owlCarousel();
$(".lf").click(function () {
    owl.trigger(\'owl.prev\');
});

$(".rt").click(function () {
    owl.trigger(\'owl.next\');
});
  
  new WOW().init();
  
  var slider = new Slider(\'#ex1\', {
                      formatter: function(value) {
                                   return \'Tól-Ig ár: \' + value;
                      }
       });
  var slider2 = new Slider(\'#ex12\', {
                      formatter: function(value) {
                                   return \'maximum szélesség: \' + value;
                      }
       });
  var slider3 = new Slider(\'#ex13\', {
                      formatter: function(value) {
                                   return \'maximum magasság: \' + value;
                      }
       });
  var slider4 = new Slider(\'#ex14\', {
                      formatter: function(value) {
                                   return \'maximum mélység/hosszúság: \' + value;
                      }
       });
</script>
'.$GAcode.'
';

 $js_section_oldal = '
<!-- javascript fajlok -->
<script src="js/lib.js"></script>
<script src="js/jquery.js"></script>
<script src="js/custom.js"></script>
<script src="js/bootstrap.min.js"></script>
<script src="js/bootstrap-slider.js"></script>
<script src="js/jquery.sticky.js"></script>
<script src="js/wow.min.js"></script>
<script src="js/owl.carousel.min.js"></script>
<script src="js/bootbox.min.js"></script>
<script type="text/javascript">
  $(".owl-carousel").owlCarousel({
  loop:true,
                slideSpeed : 200,
  paginationSpeed : 1000,
  autoPlay : 5000,
  goToFirst : true,
  goToFirstSpeed : 1000,
  stopOnHover: true,
  lazyLoad: true,
  autoWidth:true,
  navigation : false,
  navigationText : ["prev","next"],
  pagination : false,
  paginationNumbers: false,
  responsive: true,
  items : 4,
  itemsDesktop : [1199,1],
  itemsDesktopSmall : [980,3],
  itemsTablet: [768,4],
  itemsMobile : [479,3]
  })
  
  $(".tp").owlCarousel({
  loop:true,
                slideSpeed : 200,
  paginationSpeed : 1000,
  autoPlay : 5000,
  goToFirst : true,
  goToFirstSpeed : 1000,
  stopOnHover: true,
  lazyLoad: true,
  autoWidth:true,
  navigation : false,
  navigationText : ["prev","next"],
  pagination : false,
  paginationNumbers: false,
  responsive: true,
  items : 6,
  itemsDesktop : [1169,6],
  itemsDesktopSmall : [980,4],
  itemsTablet: [768,4],
  itemsMobile : [479,2]
  })
  
  owl = $(".tp").owlCarousel();
$(".lf").click(function () {
    owl.trigger(\'owl.prev\');
});

$(".rt").click(function () {
    owl.trigger(\'owl.next\');
});
  
  new WOW().init();
  
  var slider = new Slider(\'#ex1\', {
                      formatter: function(value) {
                                   return \'Tól-Ig ár: \' + value;
                      }
       });
  var slider2 = new Slider(\'#ex12\', {
                      formatter: function(value) {
                                   return \'maximum szélesség: \' + value;
                      }
       });
</script>
<link href="css/jquery.lightbox-0.5.css" rel="stylesheet" />
<script src="js/jquery.lightbox-0.5.js"></script>
<script type="text/javascript">
  $(function() {
                $(\'a.lightbox\').lightBox(); // Select all links with lightbox class
  });
</script>
'.$GAcode.'
';

$GAcode = '<!-- Google tag (gtag.js) --> 
<script async src="https://www.googletagmanager.com/gtag/js?id=G-TTV99WS7R1"></script> 

<script> 
     window.dataLayer = window.dataLayer || []; 
     function gtag(){dataLayer.push(arguments);} 
     gtag(\'js\', new Date()); 

     gtag(\'config\', \'G-TTV99WS7R1\'); 
</script>';

 // oldalfüggvények
 
function touring_header() {
    $pdoi = connect_pdo();
    print '
        
  <div class="row header">
        <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 infosection">
          <div class="row">
            <div class="col-sm-6 col-sm-offset-6 col-md-6 col-md-offset-6 col-lg-6 col-lg-offset-6">

                <span class="telefon">'.$GLOBALS['_telefon'].'</span> <span class="telefonszam">'.$GLOBALS['_telefonszam'].'</span>

                <a href="akcio_lista.php" class="akciogomb">'.$GLOBALS['_akciogomb_felirat'].'</a>

            </div>
          </div>

          <div class="row">
              
             <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
                <div class="container-1200">
                  <div class="col-lg-6 infocol">

                             <div class="topmenu">
                               <div class="row">
                                   <div class="col-xs-3 col-sm-3 col-md-3 col-lg-3 langflag">
                                       <a href="lang.php?lang=hu"><img src="/images/hun_flag.jpg" alt="hf" /></a>
                                   </div>
                                   <div class="col-xs-3 col-sm-3 col-md-3 col-lg-3 langflag">
                                       <a href="lang.php?lang=en"><img src="/images/eng_flag.jpg" alt="ef" /></a>
                                   </div>
                                   <div class="col-xs-3 col-sm-3 col-md-3 col-lg-3 langflag">
                                       <a href="lang.php?lang=de"><img src="/images/ger_flag.jpg" alt="gf" /></a>
                                   </div>
                                   <div class="col-xs-3 col-sm-3 col-md-3 col-lg-3 langflag">
                                       <a href="lang.php?lang=sk"><img src="/images/svk_flag.jpg" alt="sf" /></a>
                                   </div>
                               </div>        

                               <div class="row">
                                   <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 logo">
                                       <a href="index.php"><img src="/images/logo.jpg" alt="logo" /></a>
                                   </div>
                               </div>

                               <nav class="navbar navgomb">
                                   <div class="navbar-header">
                                       <button class="navbar-toggle" type="button" data-toggle="collapse" data-target="#menupont" aria-controls="navbarToggleExternalContent" aria-expanded="false" aria-label="Toggle navigation">
                                           <span class="icon-bar"></span>
                                           <span class="icon-bar"></span>
                                           <span class="icon-bar"></span>
                                       </button>
                                   </div>

                                
                               <!--<div class="row">-->
                                   <div id="menupont" class="col-xs-12 col-sm-12 col-md-12 col-lg-12 menupont navbar-collapse collapse">
                                       <ul class="menu-lista">
                                           ';
 
                                            $sql_selmenu = "SELECT menu_id FROM menu WHERE menu_aktiv != '0'";
                                            $params = array();
                                            $stmt = $pdoi->prepare($sql_selmenu);
                                            bindSqlPArams($stmt, $params);
                                            $ret_selmenu = $stmt->fetchAll(PDO::FETCH_ASSOC);
                                            foreach ($ret_selmenu as $rmenu) {
                                                $menu = new menu($rmenu['menu_id']);
                                                switch ($GLOBALS['lang']) {
                                                    case '1':
                                                        print '<li><a href="'.$menu->getLink().'">'.$menu->getNev1().'</a></li>';
                                                        break;
                                                    case '2':
                                                        print '<li><a href="'.$menu->getLink().'">'.$menu->getNev2().'</a></li>';
                                                        break;
                                                    case '3':
                                                        print '<li><a href="'.$menu->getLink().'">'.$menu->getNev3().'</a></li>';
                                                        break;
                                                    case '4':
                                                        print '<li><a href="'.$menu->getLink().'">'.$menu->getNev4().'</a></li>';
                                                        break;
                                                    default:
                                                        print '<li><a href="'.$menu->getLink().'">'.$menu->getNev1().'</a></li>';
                                                        break;
                                                }
                                                                  
                                            }
                                            
                                            $maidatum = date("Y-m-d");
                                            $maidatum = explode("-", $maidatum);
                                            
                                            switch ($maidatum[1]) {
                                                case "1":
                                                    $maidatum = '<span class="foglalas-ajanlatkeres-szam">'.$maidatum[2]."</span>/JAN";
                                                    break;
                                                case "2":
                                                    $maidatum = '<span class="foglalas-ajanlatkeres-szam">'.$maidatum[2]."</span>/FEB";
                                                    break;
                                                case "3":
                                                    $maidatum = '<span class="foglalas-ajanlatkeres-szam">'.$maidatum[2]."</span>/MAR";
                                                    break;
                                                case "4":
                                                    $maidatum = '<span class="foglalas-ajanlatkeres-szam">'.$maidatum[2]."</span>/APR";
                                                    break;
                                                case "5":
                                                    $maidatum = '<span class="foglalas-ajanlatkeres-szam">'.$maidatum[2]."</span>/MAJ";
                                                    break;
                                                case "6":
                                                    $maidatum = '<span class="foglalas-ajanlatkeres-szam">'.$maidatum[2]."</span>/JUN";
                                                    break;
                                                case "7":
                                                    $maidatum = '<span class="foglalas-ajanlatkeres-szam">'.$maidatum[2]."</span>/JUL";
                                                    break;
                                                case "8":
                                                    $maidatum = '<span class="foglalas-ajanlatkeres-szam">'.$maidatum[2]."</span>/AUG";
                                                    break;
                                                case "9":
                                                    $maidatum = '<span class="foglalas-ajanlatkeres-szam">'.$maidatum[2]."</span>/SEP";
                                                    break;
                                                case "10":
                                                    $maidatum = '<span class="foglalas-ajanlatkeres-szam">'.$maidatum[2]."</span>/OKT";
                                                    break;
                                                case "11":
                                                    $maidatum = '<span class="foglalas-ajanlatkeres-szam">'.$maidatum[2]."</span>/NOV";
                                                    break;
                                                case "12":
                                                    $maidatum = '<span class="foglalas-ajanlatkeres-szam">'.$maidatum[2]."</span>/DEC";
                                                    break;
                                            }
                                            
                                            $tdatum = date('Y-m-d', strtotime('+1 day'));
                                            $tdatum = explode("-", $tdatum);
                                            
                                            switch ($tdatum[1]) {
                                                case "1":
                                                    $tdatum = '<span class="foglalas-ajanlatkeres-szam">'.$tdatum[2]."</span>/JAN";
                                                    break;
                                                case "2":
                                                    $tdatum = '<span class="foglalas-ajanlatkeres-szam">'.$tdatum[2]."</span>/FEB";
                                                    break;
                                                case "3":
                                                    $tdatum = '<span class="foglalas-ajanlatkeres-szam">'.$tdatum[2]."</span>/MAR";
                                                    break;
                                                case "4":
                                                    $tdatum = '<span class="foglalas-ajanlatkeres-szam">'.$tdatum[2]."</span>/APR";
                                                    break;
                                                case "5":
                                                    $tdatum = '<span class="foglalas-ajanlatkeres-szam">'.$tdatum[2]."</span>/MAJ";
                                                    break;
                                                case "6":
                                                    $tdatum = '<span class="foglalas-ajanlatkeres-szam">'.$tdatum[2]."</span>/JUN";
                                                    break;
                                                case "7":
                                                    $tdatum = '<span class="foglalas-ajanlatkeres-szam">'.$tdatum[2]."</span>/JUL";
                                                    break;
                                                case "8":
                                                    $tdatum = '<span class="foglalas-ajanlatkeres-szam">'.$tdatum[2]."</span>/AUG";
                                                    break;
                                                case "9":
                                                    $tdatum = '<span class="foglalas-ajanlatkeres-szam">'.$tdatum[2]."</span>/SEP";
                                                    break;
                                                case "10":
                                                    $tdatum = '<span class="foglalas-ajanlatkeres-szam">'.$tdatum[2]."</span>/OKT";
                                                    break;
                                                case "11":
                                                    $tdatum = '<span class="foglalas-ajanlatkeres-szam">'.$tdatum[2]."</span>/NOV";
                                                    break;
                                                case "12":
                                                    $tdatum = '<span class="foglalas-ajanlatkeres-szam">'.$tdatum[2]."</span>/DEC";
                                                    break;
                                            }
                                         
                                    print '</ul>
                                   <!--</div>-->
                                   </div>

                               </nav>

                               <div class="row foglalas">
                                   <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 foglalas-col">
                                       
                                       <div class="foglalas-felirat">
                                           <span class="foglalas-foglalas">'.$GLOBALS['_foglalas_foglalas'].'</span><br />
                                           <span class="foglalas-online">'.$GLOBALS['_foglalas_online'].'</span>
                                           <div style="clear: both"></div> <!--clearfix-->
                                       </div>

                                            <div class="foglalas-ajanlatkeres">
                                            
                                                <form action="ajanlatkeres.php" method="post">

                                                         <div class="foglalas-ajanlatkeres-erkezes">
                                                             <div class="foglalas-ajanlatkeres-erkezes-szoveg">
                                                                 '.$GLOBALS['_foglalas_erkezes'].'
                                                             </div>
                                                             <div class="foglalas-ajanlatkeres-datum">
                                                                 <label for="kezd_datum" id="kezd_datum_label">'.$maidatum.'</label><input name="kezd_datum" id="kezd_datum" style="opacity: 0; width: 0" value="'.date('Y-m-d').'" onchange="datumjs(\'kezd_datum\');">
                                                             </div>
                                                             <div style="clear: both"></div> <!--clearfix-->
                                                         </div>

                                                         <div class="foglalas-ajanlatkeres-tavozas">
                                                             <div class="foglalas-ajanlatkeres-erkezes-szoveg">
                                                                 '.$GLOBALS['_foglalas_tavozas'].'
                                                             </div>
                                                             <div class="foglalas-ajanlatkeres-datum">
                                                                 <label for="veg_datum" id="veg_datum_label">'.$tdatum.'</label><input name="veg_datum" id="veg_datum" style="opacity: 0; width: 0" value="'.date('Y-m-d', strtotime('+1 day')).'" onchange="datumjs(\'veg_datum\');">
                                                             </div>
                                                             <div style="clear: both"></div> <!--clearfix-->
                                                         </div>

                                                         <div class="foglalas-ajanlatkeres-letszam">
                                                             <div class="foglalas-ajanlatkeres-erkezes-szoveg">
                                                                 '.$GLOBALS['_foglalas_letszam'].'
                                                             </div>
                                                             <div class="foglalas-ajanlatkeres-letszam-div">
                                                                 <!--<input type="number" name="letszam" min="0" />-->
                                                                 <select class="foglalas-select" style="width:50px;" name="letszam">
                                                                    <option value="1">1</option>
                                                                    <option value="2">2</option>
                                                                    <option value="3">3</option>
                                                                    <option value="4">4</option>
                                                                    <option value="5">5</option>
                                                                    <option value="6">6</option>
                                                                    <option value="7">7</option>
                                                                    <option value="8">8</option>
                                                                    <option value="9">9</option>
                                                                    <option value="10">10</option>
                                                                 </select>
                                                             </div>
                                                             <div style="clear: both"></div> <!--clearfix-->
                                                         </div>

                                                    <div class="foglalas-ajanlatkeres-submit">
                                                        <button class="akciogomb">'.$GLOBALS['_ajanlat_keres'].'</button>
                                                        <div style="clear: both"></div> <!--clearfix-->
                                                    </div>
                                                </form>
                                            </div>
                                       
                                       </form>

                                   </div>

                                   <div style="clear: both"></div> <!--clearfix-->
                               </div>
                               <div style="clear: both"></div> <!--clearfix-->
                           </div>

                       </div>
                     </div>
                  </div>
              
               </div>
             </div>
           </div>';
 }
 
 function touring_slider() {
     print '<div class="row sliderrow">
      
              <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 topslider">
                    <div id="owl-blog" class="owl-carousel owl-theme fadeInUp">';
                       slider();
                 print '</div><!-- owl -->
              </div>
              
      </div>';
 }
 
 function touring_ajanlatok() {
    $pdo = connect_pdo();
    print '<div class="row ajanlatok">
          <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 ajanlat-container">';
              
                    $sql_selajan = "SELECT csomag_id FROM csomag WHERE csomag_aktiv != '0' && csomag_del != '1' ORDER BY csomag_id DESC LIMIT 4";
                    $params = array();
                    $stmt = $pdo->prepare($sql_selajan);
                    bindSqlPArams($stmt, $params);
                    $ret_selajan = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    foreach ($ret_selajan as $rajan) {
                        $csomag = new csomag($rajan['csomag_id']);
                        $csomag->printAjanlatContainer((++$i%2?"paratlan":"paros"));
                    }               
              
         print '</div>
      </div>
      
      <div class="row">
        <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12" style="text-align:center;">
            <a href="akcio_lista.php" class="akciogomb" style="margin-left:auto; margin-rigth:auto;">Összes ajánlat</a>
        </div>
      </div>';
 }


 function touring_specszolg() {
     print '<div class="row specszolg">
          <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
              <a id="specszolg"></a><span class="specszolg-felsocim">'.$GLOBALS['_specszolg_miert'].'</span><br />
              <span class="specszolg-focim">'.$GLOBALS['_specszolg_cim'].'</span><br />
              
              <div class="row specszolg-kepek">
                  <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12" style="padding-left: 0px; padding-right: 0px;">

                        <div class="specszolg-kepek-kicsi">
                            <img src="/uploads/hotel.jpg">
                            <span class="specszolg-kepek-felirat">
                                <a href="hotel.php">'.$GLOBALS['_specszolg_hotel'].'</a>
                            </span>
                            <div style="clear: both"></div> <!--clearfix-->
                        </div>

                        <div class="specszolg-kepek-kicsi">
                            <img src="/uploads/etterem.jpg">
                            <div style="clear: both"></div> <!--clearfix-->
                            <span class="specszolg-kepek-felirat">
                                <a href="etterem.php">'.$GLOBALS['_specszolg_etterem'].'</a>
                            </span>
                        </div>

                        <div class="specszolg-kepek-kicsi">
                            <img src="/uploads/szoba.jpg">
                            <span class="specszolg-kepek-felirat">
                                <a href="szobak.php">'.$GLOBALS['_specszolg_szoba'].'</a>
                            </span>
                            <div style="clear: both"></div> <!--clearfix-->
                        </div>

                        <div class="specszolg-kepek-kicsi">
                            <img src="/uploads/programok.jpg">
                            <span class="specszolg-kepek-felirat">
                                <a href="rendezvenyek.php">'.$GLOBALS['_specszolg_programok'].'</a>
                            </span>
                            <div style="clear: both"></div> <!--clearfix-->
                        </div>

                        <div class="specszolg-kepek-kozepes">
                            <img src="/uploads/sparelax.jpg">
                            <span class="specszolg-kepek-felirat-kozepes">
                                <a href="furdo.php">'.$GLOBALS['_specszolg_furdo'].'</a>
                            </span>
                            <div style="clear: both"></div> <!--clearfix-->
                        </div>
                          
                      </div>
                      
              </div>
          </div>    
      </div>';
 }
 
 function touring_velemeny() {
    $pdo = connect_pdo();
    $sql_selvel = "SELECT velemeny_id FROM velemeny WHERE velemeny_aktiv != '0' ORDER BY RAND() LIMIT 1";
    $params = array();
    $stmt = $pdo->prepare($sql_selvel);
    bindSqlPArams($stmt, $params);
    $ret_selvel = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($ret_selvel as $rvel) {
        $velemeny = new velemeny($rvel['velemeny_id']);
     print '<div class="row velemeny">
          <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12 velemeny-col">
              <div class="container-1200">
                    <span class="velemeny-felsocim">'.$GLOBALS['_velemeny_felsocim'].'</span><br />
                    <span class="velemeny-focim">'.$GLOBALS['_velemeny_focim'].'</span><br />
                    <div class="csillagok"><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i><i class="fas fa-star"></i></div><br />
                    <span class="velemeny-szoveg">';
     
                        switch ($GLOBALS['lang']) {
                            case '1':
                                print $velemeny->getSzoveg1();
                                break;
                            case '2':
                                print $velemeny->getSzoveg2();
                                break;
                            case '3':
                                print $velemeny->getSzoveg3();
                                break;
                            case '4':
                                print $velemeny->getSzoveg4();
                                break;
                            default:
                                print $velemeny->getSzoveg1();
                                break;
                        }
     
             print '</span><br />
                    <div style="margin-top: 45px;">
                        <span class="velemeny-nev">'.$velemeny->getNev().'</span><br />
                    </div>
                    <span class="velemeny-datum">'.$velemeny->getDatum().'</span>
            </div>
        </div>
      </div>';
    }
 }
 
 function touring_kitekinto() {
    $pdo = connect_pdo();
    print '<div class="row kitekinto">
          <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
              <div class="container-1200" style="text-align: center;">
                  
                  <span class="kitekinto-felsocim">'.$GLOBALS['_kitekinto_felsocim'].'</span><br />
                  <span class="kitekinto-focim">'.$GLOBALS['_kitekinto_focim'].'</span>
                  
                  <div class="kitekinto-nagy">';
     
                    $datum = date("Y-m-d");
     
                    $sql_selkit = "SELECT kitekinto_id FROM kitekinto WHERE kitekinto_aktiv != '0' AND kitekinto_kezd_datum > :datum ORDER BY kitekinto_kezd_datum DESC";
                    $params = array(':datum'=>$datum);
                    $stmt = $pdo->prepare($sql_selkit);
                    bindSqlPArams($stmt, $params);
                    $ret_selkit = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    foreach ($ret_selkit as $rkit) {                    
                        $kitekinto = new kitekinto($rkit['kitekinto_id']);
                      
                  print '<div class="kitekinto-nagy-szoveg">
                          <div class="kitekinto-nagy-szoveg-fejcim">
                              '.$kitekinto->getKezdDatum().($kitekinto->getKezdDatum()==$kitekinto->getVegDatum()||$kitekinto->getVegDatum()=='0000-00-00'?"":'-'.$kitekinto->getVegDatum()).'
                          </div>
                          <div class="kitekinto-nagy-szoveg-focim">';
                            switch ($GLOBALS['lang']) {
                                case '1':
                                    print $kitekinto->getNev1();
                                    break;
                                case '2':
                                    print $kitekinto->getNev2();
                                    break;
                                case '3':
                                    print $kitekinto->getNev3();
                                    break;
                                case '4':
                                    print $kitekinto->getNev4();
                                    break;
                                default:
                                    print $kitekinto->getNev1();
                                    break;
                            }
                   print '</div>
                          
                          <div class="kitekinto-link">
                              <a href="kitekinto.php?id='.$kitekinto->getId().'">'.$GLOBALS['_kitekinto_tovabb'].'</a>
                          </div>
                      </div>
                      <div style="clear: both"></div> <!--clearfix-->
                  </div>
                  
                  <div class="kitekinto-kicsi">';
               
               print '<div class="kitekinto-kicsi-felsocim">
                          '.$kitekinto->getKezdDatum().($kitekinto->getKezdDatum()==$kitekinto->getVegDatum()||$kitekinto->getVegDatum()=='0000-00-00'?"":'-'.$kitekinto->getVegDatum()).'
                      </div>
                      
                      <div class="kitekinto-kicsi-focim">';
                          switch ($GLOBALS['lang']) {
                              case '1':
                                  print $kitekinto->getNev1();
                                  break;
                              case '2':
                                  print $kitekinto->getNev2();
                                  break;
                              case '3':
                                  print $kitekinto->getNev3();
                                  break;
                              case '4':
                                  print $kitekinto->getNev4();
                                  break;
                              default:
                                  print $kitekinto->getNev1();
                                  break;
                          }
               print '</div>
                      
                      <div class="kitekinto-kicsi-szoveg">';
                        switch ($GLOBALS['lang']) {
                            case '1':
                                print $kitekinto->getDesc1();
                                break;
                            case '2':
                                print $kitekinto->getDesc2();
                                break;
                            case '3':
                                print $kitekinto->getDesc3();
                                break;
                            case '4':
                                print $kitekinto->getDesc4();
                                break;
                            default:
                                print $kitekinto->getDesc1();
                                break;
                        }
               print '</div>
                      
                      <div class="kitekinto-link">
                              <a href="kitekinto.php?id='.$kitekinto->getId().'">'.$GLOBALS['_kitekinto_tovabb'].'</a>
                      </div>
                      
                      <div style="clear: both"></div> <!--clearfix-->
                  </div>
                  
                  <div class="kitekinto-kicsi">';
                  
                    $rkit = mysql_fetch_array($ret_selkit);
                    
                    $kitekinto = new kitekinto($rkit['kitekinto_id']);

               print '<div class="kitekinto-kicsi-felsocim">
                          '.$kitekinto->getKezdDatum().'-'.$kitekinto->getVegDatum().'
                      </div>
                      
                      <div class="kitekinto-kicsi-focim">';
                          switch ($GLOBALS['lang']) {
                              case '1':
                                  print $kitekinto->getNev1();
                                  break;
                              case '2':
                                  print $kitekinto->getNev2();
                                  break;
                              case '3':
                                  print $kitekinto->getNev3();
                                  break;
                              case '4':
                                  print $kitekinto->getNev4();
                                  break;
                              default:
                                  print $kitekinto->getNev1();
                                  break;
                          }
               print '</div>
                      
                      <div class="kitekinto-kicsi-szoveg">';
                        switch ($GLOBALS['lang']) {
                            case '1':
                                print $kitekinto->getDesc1();
                                break;
                            case '2':
                                print $kitekinto->getDesc2();
                                break;
                            case '3':
                                print $kitekinto->getDesc3();
                                break;
                            case '4':
                                print $kitekinto->getDesc4();
                                break;
                            default:
                                print $kitekinto->getDesc1();
                                break;
                        }
               print '</div>
                      
                      <div class="kitekinto-link">
                              <a href="kitekinto.php?id='.$kitekinto->getId().'">'.$GLOBALS['_kitekinto_tovabb'].'</a>
                      </div>
                      <div style="clear: both"></div> <!--clearfix-->
                  </div>
                    
              </div>
          </div>
      </div>';
    }
 }


 function touring_felsofooter() {
     print '<div class="row felsofooter">
          <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
            <div class="container-1200">
                <div class="col-xs-12 col-sm-3 col-md-3 col-lg-3">

                    <div class="felsofooter-aktualis-idojaras">
                        '.$GLOBALS['_felsofooter_idojaras'].'
                    </div>

                    <div class="felsofooter-aktualis-idojaras-leiras">
                        Napos, néhol felhős
                    </div>

                </div>

                <div class="col-xs-12 col-sm-3 col-md-3 col-lg-3">
                    <div id="ido">
                         <script type="text/javascript">
                          $(document).ready(function () {
                           $("#ido").weatherfeed(["HUXX8177"],{
                                        unit: "c",
                                        image: true,
                                        country: false,
                                        highlow: false,
                                        wind: false,
                                        humidity: false,
                                        visibility: false,
                                        sunrise: false,
                                        sunset: false,
                                        forecast: false,
                                        link: false,
                                        showerror: true	});
                          });
                         </script>
                    </div>
                </div>

                <div class="col-xs-12 col-sm-3 col-md-3 col-lg-3">
                    <div class="felsofooter-aktualis-idojaras">
                        '.$GLOBALS['_felsofooter_kovetes'].'
                    </div>

                    <div class="felsofooter-aktualis-idojaras-leiras">
                        '.$GLOBALS['_felsofooter_szocmed'].'
                    </div>
                </div>

                <div class="col-xs-12 col-sm-3 col-md-3 col-lg-3 felsofooter-szocmed-gomb">
                    <a href="https://www.facebook.com/profile.php?id=61552966991982" target="_blank"><i class="fab fa-facebook-f"></i></a>
                    <!--<i class="fab fa-google"></i>--><a href="https://www.instagram.com/touring_hotel/?hl=hu" target="_blank"><i class="fab fa-instagram"></i></a>
                </div>
            </div>
          </div>    
      </div>';
 }
 
 function touring_alsofooter() {
     print '<div class="row alsofooter">
          <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
            <div class="container-1200">
                <div class="col-xs-12 col-sm-4 col-md-4 col-lg-4 alsofooter-bal">
                      <div>
                          Touring Hotel** - BEREKFÜRDŐ
                      </div>
                      <div>
                          Telefon: +30 567 4270 cím: Berekfürdő, Berek tér 13<br />
                          NTAK: Sz23062692
                      </div>
                </div>

                <div class="col-xs-12 col-sm-4 col-md-4 col-lg-4 alsofooter-kozep">
                    <img src="/uploads/footlogo.jpg" />
                </div>

                <div class="col-xs-12 col-sm-4 col-md-4 col-lg-4 alsofooter-jobb">
                    <div>
                        '.$GLOBALS['_alsofooter_minden_jog'].'
                    </div>

                    <div>
                        <a href="aszf.php">'.$GLOBALS['_alsofooter_aszf'].'</a> / <a href="jogi.php">'.$GLOBALS['_alsofooter_jogi'].'</a> / <a href="kapcsolat.php">'.$GLOBALS['_alsofooter_kapcsolat'].'</a>
                    </div>
                </div>
            </div>
          </div>    
      </div>';
 }
 
 function touring_suti() {
   if ( $_COOKIE['suti'] != "1" ) {
     print '<div id="suti" style="display:block;">
              <div class="container-1200">
                <a href="javascript:sutiWarn();" class="sutigomb">Megértettem</a>
                <div style="padding:15px;">A Touring Hotel** weboldalán úgynevezett <a href="https://hu.wikipedia.org/wiki/HTTP-s%C3%BCti">sütiket</a> használunk a jobb felhasználói élmény és a weboldal működésének megfigyelése érdekében! Ha folytatja a böngészést weboldalunkon, akkor úgy tekintjük, hogy beleegyezik a sütik használatába! <a href="aszf.php">További információk, adatvédelmi szabályzat</a></div>
              </div>
            </div>';
    }
 }
?>
