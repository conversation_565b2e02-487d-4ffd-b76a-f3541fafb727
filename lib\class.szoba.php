<?php

//touring szoba nyehehe

class szoba {
    
    private $id;
    private $nev_1;
    private $nev_2;
    private $nev_3;
    private $nev_4;
    private $seo_key;
    private $seo_desc;
    private $kep;
    private $desc_1;
    private $desc_2;
    private $desc_3;
    private $desc_4;
    private $rovdesc_1;
    private $rovdesc_2;
    private $rovdesc_3;
    private $rovdesc_4;
    private $darab;
    private $ar;
    private $galeria;
    private $aktiv;
    
    public function __construct($id) {
        $pdo = connect_pdo();
        $sql_selszoba = "SELECT * FROM szoba WHERE szoba_id = :id";
        $params_szoba = array(':id'=>$id);
        $stmt_szoba = $pdo->prepare($sql_selszoba);
        bindSqlPArams($stmt_szoba, $params_szoba);
        $ret_selszoba = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($ret_selszoba as $rszoba) {
            $this->id = $id;
            $this->nev_1 = $rszoba['szoba_nev_1'];
            $this->nev_2 = $rszoba['szoba_nev_2'];
            $this->nev_3 = $rszoba['szoba_nev_3'];
            $this->nev_4 = $rszoba['szoba_nev_4'];
            $this->seo_key = $rszoba['szoba_seo_key'];
            $this->seo_desc = $rszoba['szoba_seo_desc'];
            $this->kep[] = $rszoba['szoba_kep'];
            $this->desc_1 = $rszoba['szoba_desc_1'];
            $this->desc_2 = $rszoba['szoba_desc_2'];
            $this->desc_3 = $rszoba['szoba_desc_3'];
            $this->desc_4 = $rszoba['szoba_desc_4'];
            $this->rovdesc_1 = $rszoba['szoba_rovdesc_1'];
            $this->rovdesc_2 = $rszoba['szoba_rovdesc_2'];
            $this->rovdesc_3 = $rszoba['szoba_rovdesc_3'];
            $this->rovdesc_4 = $rszoba['szoba_rovdesc_4'];
            $this->darab = $rszoba['szoba_darab'];
            $this->ar = $rszoba['szoba_ar'];
            $this->galeria = $rszoba['szoba_galeria'];
            $this->aktiv = $rszoba['szoba_aktiv'];
        }
    }
    
    //getterek
    
    function getId() {     
        return $this->id;        
    }
    
    function getNev1() {       
        return $this->nev_1;       
    }
    
    function getNev2() {       
        return $this->nev_2;       
    }
    
    function getNev3() {       
        return $this->nev_3;       
    }
    
    function getNev4() {       
        return $this->nev_4;       
    }
    
    function getSeoKey() {
        return $this->seo_key;
    }
    
    function getSeoDesc() {
        return $this->seo_desc;
    }
    
    function getKep() {
        return $this->kep;
    }
    
    function getDesc1() {
        return $this->desc_1;
    }
    
    function getDesc2() {
        return $this->desc_2;
    }
    
    function getDesc3() {
        return $this->desc_3;
    }
    
    function getDesc4() {
        return $this->desc_4;
    }
    
    function getRovDesc1() {
        return $this->rovdesc_1;
    }
    
    function getRovDesc2() {
        return $this->rovdesc_2;
    }
    
    function getRovDesc3() {
        return $this->rovdesc_3;
    }
    
    function getRovDesc4() {
        return $this->rovdesc_4;
    }
    
    function getDarab() {
        return $this->darab;
    }
    
    function getAr() {
        return $this->ar;
    }
    
    function getGaleria() {
        return $this->galeria;
    }
    
    function getAktiv() {
        return $this->aktiv;
    }
    
    //setterek
    
    function setNev1($nev) {
        $this->nev_1 = $nev;
    }
    
    function setNev2($nev) {
        $this->nev_2 = $nev;
    }
    
    function setNev3($nev) {
        $this->nev_3 = $nev;
    }
    
    function setNev4($nev) {
        $this->nev_4 = $nev;
    }
    
    function setSeoKey($seo_key) {
        $this->seo_key = $seo_key;
    }
    
    function setSeoDesc($seo_desc) {
        $this->seo_desc = $seo_desc;
    }
    
    function setKep($kep) {
        $this->kep = $kep;
    }
    
    function setDesc1($desc) {
        $this->desc_1 = $desc;
    }
    
    function setDesc2($desc) {
        $this->desc_2 = $desc;
    }
    
    function setDesc3($desc) {
        $this->desc_3 = $desc;
    }
    
    function setDesc4($desc) {
        $this->desc_4 = $desc;
    }
    
    function setRovDesc1($rovdesc) {
        $this->rovdesc_1 = $rovdesc;
    }
    
    function setRovDesc2($rovdesc) {
        $this->rovdesc_2 = $rovdesc;
    }
    
    function setRovDesc3($rovdesc) {
        $this->rovdesc_3 = $rovdesc;
    }
    
    function setRovDesc4($rovdesc) {
        $this->rovdesc_4 = $rovdesc;
    }
    
    function setDarab($darab) {
        $this->darab = $darab;
    }
    
    function setAr($ar) {
        $this->ar = $ar;
    }
    
    function setGaleria($galeria) {
        $this->galeria = $galeria;
    }
    
    function setAktiv($aktiv) {
        $this->aktiv = $aktiv;
    }
            
    function update() {
        $pdo = connect_pdo();
        $kepek = $this->getKep()[0];        
        $sql_updszoba = "UPDATE szoba SET szoba_nev_1 = '$this->nev_1', szoba_nev_2 = '$this->nev_2', szoba_nev_3 = '$this->nev_3', szoba_nev_4 = '$this->nev_4', szoba_seo_key = '$this->seo_key', szoba_seo_desc = '$this->seo_desc', szoba_kep = '$kepek', szoba_desc_1 = '$this->desc_1', szoba_desc_2 = '$this->desc_2', szoba_desc_3 = '$this->desc_3', szoba_desc_4 = '$this->desc_4', szoba_rovdesc_1 = '$this->rovdesc_1', szoba_rovdesc_2 = '$this->rovdesc_2', szoba_rovdesc_3 = '$this->rovdesc_3', szoba_rovdesc_4 = '$this->rovdesc_4', szoba_darab = '$this->darab', szoba_ar = '$this->ar', szoba_galeria = '$this->galeria', szoba_aktiv = '$this->aktiv' WHERE szoba_id = '$this->id'";
        $params_szoba = array(':nev_1'=>$this->nev_1, ':nev_2'=>$this->nev_2, ':nev_3'=>$this->nev_3, ':nev_4'=>$this->nev_4, ':seo_key'=>$this->seo_key, ':seo_desc'=>$this->seo_desc, ':kep'=>$kepek, ':desc_1'=>$this->desc_1, ':desc_2'=>$this->desc_2, ':desc_3'=>$this->desc_3, ':desc_4'=>$this->desc_4, ':rovdesc_1'=>$this->rovdesc_1, ':rovdesc_2'=>$this->rovdesc_2, ':rovdesc_3'=>$this->rovdesc_3, ':rovdesc_4'=>$this->rovdesc_4, ':darab'=>$this->darab, ':ar'=>$this->ar, ':galeria'=>$this->galeria, ':aktiv'=>$this->aktiv, ':id'=>$this->id);
        $stmt_szoba = $pdo->prepare($sql_updszoba);        
        
        if ( bindSqlPArams($stmt_szoba, $params_szoba) ) {
            return TRUE;
        } else {
            return FALSE;
        }
        
    }
    
    function printSzobaLista() {
        print '<div class="col-xs-12 col-sm-12 col-md-12 col-lg-4 szoba">
                        <div class="etterem-etelkep">
                            <img src="/szobakep/tn_'.$this->getKep()[0].'" alt="szobakep" />
                        </div>
                        <div class="szoba-nev">';
                            
                            switch ($GLOBALS['lang']) {
                                case "1":
                                    print $this->getNev1();
                                    break;
                                case "2":
                                    print $this->getNev2();
                                    break;
                                case "3":
                                    print $this->getNev3();
                                    break;
                                case "4":
                                    print $this->getNev4();
                                    break;
                                default :
                                    print $this->getNev1();
                                    break;
                            }
        
                 print '</div>
                        <a href="szoba.php?id='.$this->getId().'" class="feher-narancsborder-gomb">'.$GLOBALS['_lefoglalom_gomb'].'</a>
                    </div>';
    }
    
}