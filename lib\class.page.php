<?php

//touring szöveges oldalak wakawaka

class page {
    
    private $id;
    private $nev;
    private $seo_key;
    private $seo_desc;
    private $desc_1;
    private $desc_2;
    private $desc_3;
    private $desc_4;
    private $aktiv;
    
    public function __construct($nev) {
        $pdo = connect_pdo();
        
        $sql_selpage = "SELECT * FROM page WHERE page_nev = :nev";
        $params_page = array(':nev'=>$nev);
        $stmt_page = $pdo->prepare($sql_selpage);
        debugMySql($stmt_page);
        bindSqlPArams($stmt_page, $params_page);
        $ret_selpage = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($ret_selpage as $rpage) {
            $this->id = $rpage['page_id'];
            $this->nev = $nev;
            $this->seo_key = $rpage['page_seo_key'];
            $this->seo_desc = $rpage['page_seo_desc'];
            $this->desc_1 = $rpage['page_desc_1'];
            $this->desc_2 = $rpage['page_desc_2'];
            $this->desc_3 = $rpage['page_desc_3'];
            $this->desc_4 = $rpage['page_desc_4'];
            $this->aktiv = $rpage['page_aktiv'];
        }
    }
    
    //getterek
    
    function getId() {
        return $this->id;
    }
    
    function getNev() {
        return $this->nev;
    }
    
    function getSeoKey() {
        return $this->seo_key;
    }
    
    function getSeoDesc() {
        return $this->seo_desc;
    }
    
    function getDesc1() {
        return $this->desc_1;
    }
    
    function getDesc2() {
        return $this->desc_2;
    }
    
    function getDesc3() {
        return $this->desc_3;
    }
    
    function getDesc4() {
        return $this->desc_4;
    }
    
    function getAktiv() {
        return $this->aktiv;
    }
    
    //setterek
    
    function setNev($nev) {
        $this->nev = $nev;
    }
    
    function setSeoKey($seo_key) {
        $this->seo_key = $seo_key;
    }
    
    function setSeoDesc($seo_desc) {
        $this->seo_desc = $seo_desc;
    }
    
    function setDesc1($desc) {
        $this->desc_1 = $desc;
    }
    
    function setDesc2($desc) {
        $this->desc_2 = $desc;
    }
    
    function setDesc3($desc) {
        $this->desc_3 = $desc;
    }
    
    function setDesc4($desc) {
        $this->desc_4 = $desc;
    }
    
    function setAktiv($aktiv) {
        $this->aktiv = $aktiv;
    }
    
    function update() {
        $pdo = connect_pdo();
        $sql_updpage = "UPDATE page SET page_seo_key = :seo_key, page_seo_desc = :seo_desc, page_desc_1 = :desc_1, page_desc_2 = :desc_2, page_desc_3 = :desc_3, page_desc_4 = :desc_4, page_aktiv = :aktiv WHERE page_id = :id";
        $params_page = array(':seo_key'=>$this->seo_key, ':seo_desc'=>$this->seo_desc, ':desc_1'=>$this->desc_1, ':desc_2'=>$this->desc_2, ':desc_3'=>$this->desc_3, ':desc_4'=>$this->desc_4, ':aktiv'=>$this->aktiv, ':id'=>$this->id);
        $stmt_page = $pdo->prepare($sql_updpage);
        
        if ( bindSqlPArams($stmt_page, $params_page) ) {
            return TRUE;
        } else {
            return FALSE;
        }
    }
}